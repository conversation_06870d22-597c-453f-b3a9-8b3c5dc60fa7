@echo off
REM Flutter Test Coverage Script for Windows

echo 🧪 Running comprehensive test suite with coverage...

REM Install dependencies
echo 📦 Installing dependencies...
flutter pub get

REM Run static analysis
echo 🔍 Running static analysis...
flutter analyze

REM Format code
echo 🎨 Formatting code...
dart format --fix .

REM Clean previous coverage
echo 🧹 Cleaning previous coverage...
if exist coverage rmdir /s /q coverage

REM Run unit and widget tests with coverage
echo 🔬 Running unit and widget tests with coverage...
flutter test --coverage

REM Run integration tests
echo 🏗️ Running integration tests...
flutter test integration_test/

echo ✅ Test suite completed!
echo 📈 Coverage report available at: coverage/lcov.info

REM Display basic coverage info if lcov.info exists
if exist coverage\lcov.info (
    echo 📊 Coverage data generated successfully
    echo Check coverage/lcov.info for detailed coverage information
) else (
    echo ⚠️ No coverage data generated
)