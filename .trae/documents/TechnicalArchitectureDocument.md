## 1. Architecture design

```mermaid
graph TD
    A[User Mobile Device] --> B[Flutter Application]
    B --> C[Local JSON Storage]
    B --> D[SQLite Database]
    B --> E[Asset Manager]
    
    subgraph "Frontend Layer"
        B
    end
    
    subgraph "Data Layer"
        C
        D
        E
    end
    
    subgraph "Local Storage"
        F[Articles JSON]
        G[Feedstock JSON]
        H[Images/Assets]
        I[User Preferences]
    end
    
    C --> F
    C --> G
    E --> H
    D --> I
```

## 2. Technology Description

* Frontend: Flutter\@3.16+ + Dart\@3.2+

* Local Storage: JSON files + <SQLite@3.x>

* State Management: <Provider@6.x>

* UI Components: Material Design 3

* Image Handling: Cached Network <Image@3.x>

* Database: <sqflite@2.x> for local preferences

## 3. Route definitions

| Route            | Purpose                                            |
| ---------------- | -------------------------------------------------- |
| /home            | Home page with welcome screen and quick navigation |
| /knowledge       | Knowledge Hub with article categories and reader   |
| /calculator      | Biochar Calculator with input forms and results    |
| /feedstock       | Feedstock Database with searchable cards           |
| /carbon-tracking | Carbon Credit Tracking with estimation tools       |
| /article/:id     | Individual article reader with content display     |
| /feedstock/:id   | Detailed feedstock information view                |

## 4. API definitions

Since this is a fully offline application, there are no external API calls. All data interactions are with local storage.

### 4.1 Core Data Models

**Article Model**

```dart
class Article {
  final String id;
  final String category;
  final String title;
  final List<ContentSection> content;
  final String lastUpdated;
}

class ContentSection {
  final String section;
  final String text;
}
```

**Feedstock Model**

```dart
class Feedstock {
  final String id;
  final String name;
  final String thumbnail;
  final YieldRange yieldRange;
  final String pyrolysisTemp;
  final String considerations;
}

class YieldRange {
  final double min;
  final double max;
}
```

**Calculator Models**

```dart
class CalculatorInput {
  final String feedstockId;
  final double weightKg;
  final String tempRange;
}

class CalculatorOutput {
  final double yieldKg;
  final double carbonStorageKgCo2Eq;
}
```

## 5. Server architecture diagram

Not applicable - this is a fully offline mobile application with no server components.

## 6. Data model

### 6.1 Data model definition

```mermaid
erDiagram
    ARTICLE {
        string id PK
        string category
        string title
        json content
        string last_updated
    }
    
    FEEDSTOCK {
        string id PK
        string name
        string thumbnail
        json yield_range
        string pyrolysis_temp
        string considerations
    }
    
    USER_PREFERENCES {
        string id PK
        string key
        string value
        string created_at
    }
    
    CALCULATOR_HISTORY {
        string id PK
        string feedstock_id
        double weight_kg
        string temp_range
        double yield_kg
        double carbon_storage
        string created_at
    }
    
    FEEDSTOCK ||--o{ CALCULATOR_HISTORY : uses
```

### 6.2 Data Definition Language

**SQLite Tables for User Data**

```sql
-- User preferences table
CREATE TABLE user_preferences (
    id TEXT PRIMARY KEY,
    key TEXT NOT NULL,
    value TEXT NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Calculator history table
CREATE TABLE calculator_history (
    id TEXT PRIMARY KEY,
    feedstock_id TEXT NOT NULL,
    weight_kg REAL NOT NULL,
    temp_range TEXT NOT NULL,
    yield_kg REAL NOT NULL,
    carbon_storage REAL NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_calculator_history_created_at ON calculator_history(created_at DESC);
CREATE INDEX idx_user_preferences_key ON user_preferences(key);
```

**JSON Data Structure Examples**

**Articles JSON (assets/data/articles.json)**

```json
{
  "articles": [
    {
      "id": "what-is-biochar",
      "category": "basics",
      "title": "What is Biochar?",
      "content": [
        {
          "section": "Introduction",
          "text": "Biochar is a carbon-rich material produced by heating biomass in a low-oxygen environment..."
        }
      ],
      "last_updated": "2024-01-15"
    }
  ]
}
```

**Feedstock JSON (assets/data/feedstock.json)**

```json
{
  "feedstocks": [
    {
      "id": "wood-chips",
      "name": "Wood Chips",
      "thumbnail": "assets/images/wood_chips.png",
      "yield_range": {
        "min": 0.15,
        "max": 0.25
      },
      "pyrolysis_temp": "Medium (400-500°C)",
      "considerations": "Ensure chips are dry (<20% moisture) for optimal yield"
    }
  ]
}
```

**Calculation Coefficients JSON (assets/data/coefficients.json)**

```json
{
  "yield_coefficients": {
    "wood-chips": {
      "low": 0.15,
      "medium": 0.20,
      "high": 0.25
    }
  },
  "carbon_coefficients": {
    "wood-chips": {
      "co2_factor": 3.67
    }
  }
}
```

