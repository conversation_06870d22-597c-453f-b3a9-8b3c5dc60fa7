## 1. Product Overview
The Biochar App is a cross-platform mobile application that educates users about biochar production, provides yield estimation tools, and offers reference data on biomass feedstocks. It targets farmers, researchers, and sustainability enthusiasts in low-connectivity rural areas where internet access is unreliable.
- The app solves the problem of limited access to practical biochar knowledge by offering a fully offline experience with no login required.
- The product empowers users to understand biochar production, improve soil health, and contribute to carbon sequestration through an accessible mobile platform.

## 2. Core Features

### 2.1 User Roles
| Role | Registration Method | Core Permissions |
|------|---------------------|------------------|
| All Users | No registration required | Full access to all features offline |

### 2.2 Feature Module
Our Biochar app consists of the following main pages:
1. **Home page**: welcome screen, quick access navigation, app overview.
2. **Knowledge Hub page**: article categories, article reader, content filtering.
3. **Calculator page**: feedstock selection, input forms, yield calculations, results display.
4. **Feedstock Database page**: searchable cards, detailed feedstock information, filtering options.
5. **Carbon Credit Tracking page**: carbon storage estimation, tracking tools, environmental impact metrics.

### 2.3 Page Details
| Page Name | Module Name | Feature description |
|-----------|-------------|---------------------|
| Home page | Welcome section | Display app overview, quick navigation buttons to main features |
| Home page | Quick access | Provide shortcuts to Calculator and Knowledge Hub for immediate use |
| Knowledge Hub page | Article categories | Filter articles by categories (What is Biochar, How to Make Biochar, Applications, Benefits) |
| Knowledge Hub page | Article reader | Display preloaded JSON articles with scrollable content and navigation |
| Knowledge Hub page | Making Biochar guide | Include detailed instructions for Pit Method and Tin Can Method |
| Calculator page | Feedstock selection | Dropdown menu with available biomass materials from local database |
| Calculator page | Input form | Text field for weight (kg), radio buttons for temperature range (Low/Medium/High) |
| Calculator page | Calculation engine | Process inputs using fixed coefficients to estimate yield and carbon storage |
| Calculator page | Results display | Show biochar yield (kg) and carbon storage (kg CO₂-eq) with clear formatting |
| Feedstock Database page | Search functionality | Text search through feedstock names and properties |
| Feedstock Database page | Feedstock cards | Scrollable grid of cards with thumbnail, name, and key properties |
| Feedstock Database page | Detail view | Tap-to-view detailed information including yield range, temperature, considerations |
| Carbon Credit Tracking page | Carbon estimation | Calculate potential carbon credits based on biochar production |
| Carbon Credit Tracking page | Tracking tools | Monitor and record carbon sequestration over time |

## 3. Core Process
**Primary User Flow:**
Users open the app and access the Home page with navigation options. They can learn about biochar through the Knowledge Hub by selecting categories and reading articles. For practical application, users navigate to the Calculator, select a feedstock type, input weight and temperature, then view estimated yield and carbon storage. Users explore available materials through the Feedstock Database by searching or browsing cards and viewing detailed information. Carbon Credit Tracking allows users to estimate and monitor their environmental impact.

```mermaid
graph TD
    A[Home Page] --> B[Knowledge Hub]
    A --> C[Calculator]
    A --> D[Feedstock Database]
    A --> E[Carbon Credit Tracking]
    B --> F[Article Categories]
    F --> G[Article Reader]
    C --> H[Feedstock Selection]
    H --> I[Input Form]
    I --> J[Results Display]
    D --> K[Search/Browse]
    K --> L[Feedstock Details]
    E --> M[Carbon Estimation]
    M --> N[Tracking Dashboard]
```

## 4. User Interface Design
### 4.1 Design Style
- Primary colors: Earth tones (#8B4513 brown, #228B22 forest green)
- Secondary colors: #F5DEB3 wheat, #DEB887 burlywood for backgrounds
- Button style: Rounded corners with subtle shadows for accessibility
- Font: System default with large text sizes (16sp minimum) for readability
- Layout style: Card-based design with bottom navigation bar
- Icon style: Simple, outlined icons with earth/nature themes

### 4.2 Page Design Overview
| Page Name | Module Name | UI Elements |
|-----------|-------------|-------------|
| Home page | Welcome section | Large title, earth-tone gradient background, feature overview cards |
| Home page | Quick access | Grid of prominent buttons with icons, consistent spacing |
| Knowledge Hub page | Article categories | Horizontal scrollable chips with category names, active state highlighting |
| Knowledge Hub page | Article reader | Clean typography, scrollable content, progress indicator |
| Calculator page | Input form | Dropdown with search, number input with validation, radio button groups |
| Calculator page | Results display | Large numbers in cards, color-coded results, share functionality |
| Feedstock Database page | Search bar | Prominent search field with filter icon, real-time results |
| Feedstock Database page | Feedstock cards | Grid layout, thumbnail images, key info overlay |
| Carbon Credit Tracking page | Metrics display | Dashboard-style cards with charts, progress indicators |

### 4.3 Responsiveness
The app is designed mobile-first with touch-optimized interactions. All UI elements are sized for easy thumb navigation with minimum 44dp touch targets. The interface adapts to different screen sizes while maintaining consistent earth-tone theming and accessibility features.