import 'package:flutter/material.dart';
import '../models/feedstock.dart';
import '../services/feedstock_service.dart';
import '../widgets/feedstock_card.dart';
import 'feedstock_detail_screen.dart';

class FeedstockScreen extends StatefulWidget {
  const FeedstockScreen({super.key});

  @override
  State<FeedstockScreen> createState() => _FeedstockScreenState();
}

class _FeedstockScreenState extends State<FeedstockScreen> {
  List<Feedstock> _feedstocks = [];
  List<Feedstock> _filteredFeedstocks = [];
  List<String> _categories = [];
  String _selectedCategory = 'All';
  String _searchQuery = '';
  bool _isLoading = true;
  String? _error;

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadFeedstocks();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadFeedstocks() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final feedstocks = await FeedstockService.loadFeedstocks();
      final categories = await FeedstockService.getCategories();

      setState(() {
        _feedstocks = feedstocks;
        _filteredFeedstocks = feedstocks;
        _categories = ['All', ...categories];
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _filterFeedstocks() {
    setState(() {
      _filteredFeedstocks = _feedstocks.where((feedstock) {
        final matchesCategory = _selectedCategory == 'All' ||
            feedstock.category == _selectedCategory;
        final matchesSearch = _searchQuery.isEmpty ||
            feedstock.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            feedstock.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            feedstock.type.toLowerCase().contains(_searchQuery.toLowerCase());
        return matchesCategory && matchesSearch;
      }).toList();
    });
  }

  void _onCategoryChanged(String? category) {
    if (category != null) {
      setState(() {
        _selectedCategory = category;
      });
      _filterFeedstocks();
    }
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _filterFeedstocks();
  }

  void _navigateToDetail(Feedstock feedstock) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FeedstockDetailScreen(feedstock: feedstock),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Feedstock Database'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadFeedstocks,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Theme.of(context).colorScheme.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading feedstocks',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _error!,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadFeedstocks,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    // Search and Filter Section
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // Search Bar
                          TextField(
                            controller: _searchController,
                            decoration: InputDecoration(
                              hintText: 'Search feedstocks...',
                              prefixIcon: const Icon(Icons.search),
                              suffixIcon: _searchQuery.isNotEmpty
                                  ? IconButton(
                                      icon: const Icon(Icons.clear),
                                      onPressed: () {
                                        _searchController.clear();
                                        _onSearchChanged('');
                                      },
                                    )
                                  : null,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            onChanged: _onSearchChanged,
                          ),
                          const SizedBox(height: 12),
                          // Category Filter
                          Row(
                            children: [
                              const Text(
                                'Category:',
                                style: TextStyle(fontWeight: FontWeight.w500),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: DropdownButtonFormField<String>(
                                  value: _selectedCategory,
                                  decoration: InputDecoration(
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                  ),
                                  items: _categories.map((category) {
                                    return DropdownMenuItem(
                                      value: category,
                                      child: Text(category),
                                    );
                                  }).toList(),
                                  onChanged: _onCategoryChanged,
                                ),
                              ),
                            ],
                          ),
                          // Results Count
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  '${_filteredFeedstocks.length} feedstock${_filteredFeedstocks.length != 1 ? 's' : ''} found',
                                  style: Theme.of(context).textTheme.bodySmall,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (_searchQuery.isNotEmpty || _selectedCategory != 'All')
                                TextButton(
                                  onPressed: () {
                                    _searchController.clear();
                                    setState(() {
                                      _searchQuery = '';
                                      _selectedCategory = 'All';
                                    });
                                    _filterFeedstocks();
                                  },
                                  child: const Text('Clear filters'),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // Feedstock List
                    Expanded(
                      child: _filteredFeedstocks.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.search_off,
                                    size: 64,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withOpacity(0.5),
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No feedstocks found',
                                    style: Theme.of(context)
                                        .textTheme
                                        .headlineSmall,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Try adjusting your search or filters',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSurface
                                              .withOpacity(0.7),
                                        ),
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              padding: const EdgeInsets.all(16),
                              itemCount: _filteredFeedstocks.length,
                              itemBuilder: (context, index) {
                                final feedstock = _filteredFeedstocks[index];
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 12),
                                  child: FeedstockCard(
                                    feedstock: feedstock,
                                    onTap: () => _navigateToDetail(feedstock),
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
    );
  }
}