import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import '../models/feedstock.dart';

class FeedstockDetailScreen extends StatelessWidget {
  final Feedstock feedstock;

  const FeedstockDetailScreen({
    super.key,
    required this.feedstock,
  });

  Color _getAvailabilityColor(String availability) {
    switch (availability.toLowerCase()) {
      case 'high':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getSustainabilityColor(double score) {
    if (score >= 8.0) return Colors.green;
    if (score >= 6.0) return Colors.lightGreen;
    if (score >= 4.0) return Colors.orange;
    return Colors.red;
  }

  void _shareFeedstock() {
    final shareText = '''
🌾 Feedstock: ${feedstock.name}

📋 Type: ${feedstock.type}
🔥 Biochar Yield: ${feedstock.biocharYield.toStringAsFixed(1)}%
📊 Availability: ${feedstock.availability}
♻️ Sustainability Score: ${feedstock.sustainabilityScore.toStringAsFixed(1)}/10

Discover more feedstock options for biochar production in the Biochar App!
''';
    
    Share.share(
      shareText,
      subject: 'Feedstock: ${feedstock.name}',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(feedstock.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareFeedstock(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                feedstock.name,
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                feedstock.type,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: _getAvailabilityColor(feedstock.availability).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: _getAvailabilityColor(feedstock.availability),
                            ),
                          ),
                          child: Text(
                            feedstock.availability,
                            style: TextStyle(
                              color: _getAvailabilityColor(feedstock.availability),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      feedstock.description,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 8,
                      children: [
                        Chip(
                          label: Text(feedstock.category),
                          backgroundColor: Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                        ),
                        if (feedstock.seasonality.isNotEmpty)
                          Chip(
                            label: Text(feedstock.seasonality),
                            backgroundColor: Colors.blue.withOpacity(0.1),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Composition Properties
            _SectionCard(
              title: 'Composition Properties',
              icon: Icons.science,
              children: [
                _PropertyRow('Carbon Content', '${feedstock.carbonContent.toStringAsFixed(1)}%'),
                _PropertyRow('Moisture Content', '${feedstock.moistureContent.toStringAsFixed(1)}%'),
                _PropertyRow('Ash Content', '${feedstock.ashContent.toStringAsFixed(1)}%'),
                _PropertyRow('Volatile Matter', '${feedstock.volatileMatter.toStringAsFixed(1)}%'),
                _PropertyRow('Fixed Carbon', '${feedstock.fixedCarbon.toStringAsFixed(1)}%'),
              ],
            ),
            const SizedBox(height: 16),
            // Physical Properties
            _SectionCard(
              title: 'Physical Properties',
              icon: Icons.straighten,
              children: [
                _PropertyRow('Bulk Density', '${feedstock.bulkDensity.toStringAsFixed(0)} kg/m³'),
                _PropertyRow('Energy Content', '${feedstock.energyContent.toStringAsFixed(1)} MJ/kg'),
                _PropertyRow('Biochar Yield', '${feedstock.biocharYield.toStringAsFixed(1)}%'),
                _PropertyRow('Pyrolysis Temperature', '${feedstock.pyrolysisTemperature.toStringAsFixed(0)}°C'),
              ],
            ),
            const SizedBox(height: 16),
            // Economic Information
            _SectionCard(
              title: 'Economic Information',
              icon: Icons.attach_money,
              children: [
                if (feedstock.cost > 0)
                  _PropertyRow('Cost', '\$${feedstock.cost.toStringAsFixed(2)}/${feedstock.costUnit}'),
                if (feedstock.processingRequirements.isNotEmpty)
                  _PropertyRow('Processing Requirements', feedstock.processingRequirements.join(', ')),
              ],
            ),
            const SizedBox(height: 16),
            // Sustainability
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.eco,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Sustainability Score',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: LinearProgressIndicator(
                            value: feedstock.sustainabilityScore / 10,
                            backgroundColor: Colors.grey.withOpacity(0.3),
                            valueColor: AlwaysStoppedAnimation<Color>(
                              _getSustainabilityColor(feedstock.sustainabilityScore),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          '${feedstock.sustainabilityScore.toStringAsFixed(1)}/10',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: _getSustainabilityColor(feedstock.sustainabilityScore),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Advantages
            if (feedstock.advantages.isNotEmpty)
              _SectionCard(
                title: 'Advantages',
                icon: Icons.thumb_up,
                children: feedstock.advantages
                    .map((advantage) => _BulletPoint(advantage, Colors.green))
                    .toList(),
              ),
            const SizedBox(height: 16),
            // Disadvantages
            if (feedstock.disadvantages.isNotEmpty)
              _SectionCard(
                title: 'Disadvantages',
                icon: Icons.thumb_down,
                children: feedstock.disadvantages
                    .map((disadvantage) => _BulletPoint(disadvantage, Colors.orange))
                    .toList(),
              ),
          ],
        ),
      ),
    );
  }
}

class _SectionCard extends StatelessWidget {
  final String title;
  final IconData icon;
  final List<Widget> children;

  const _SectionCard({
    required this.title,
    required this.icon,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }
}

class _PropertyRow extends StatelessWidget {
  final String label;
  final String value;

  const _PropertyRow(this.label, this.value);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}

class _BulletPoint extends StatelessWidget {
  final String text;
  final Color color;

  const _BulletPoint(this.text, this.color);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6, right: 8),
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}