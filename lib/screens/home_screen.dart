import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class HomeScreen extends StatelessWidget {
  final Function(int)? onNavigate;
  
  const HomeScreen({super.key, this.onNavigate});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Biochar Calculator'),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.primaryBrown.withOpacity(0.1),
                    AppTheme.forestGreen.withOpacity(0.1),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppTheme.primaryBrown.withOpacity(0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.eco,
                        size: 32,
                        color: AppTheme.forestGreen,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Welcome to Biochar Calculator',
                          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                            color: AppTheme.primaryBrown,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Your comprehensive tool for biochar production calculations, feedstock analysis, and carbon sequestration tracking.',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: AppTheme.charcoal.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Quick Access Section
            Text(
              'Quick Access',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: AppTheme.primaryBrown,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Quick Access Grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.2,
              children: [
                _buildQuickAccessCard(
                  context,
                  icon: Icons.calculate,
                  title: 'Calculator',
                  subtitle: 'Calculate biochar yield',
                  color: AppTheme.primaryBrown,
                  onTap: () {
                    // Navigate to calculator tab
                    onNavigate?.call(2);
                  },
                ),
                _buildQuickAccessCard(
                  context,
                  icon: Icons.library_books,
                  title: 'Knowledge Hub',
                  subtitle: 'Learn about biochar',
                  color: AppTheme.forestGreen,
                  onTap: () {
                    // Navigate to knowledge tab
                    onNavigate?.call(1);
                  },
                ),
                _buildQuickAccessCard(
                  context,
                  icon: Icons.grass,
                  title: 'Feedstock DB',
                  subtitle: 'Browse materials',
                  color: AppTheme.darkGreen,
                  onTap: () {
                    // Navigate to feedstock tab
                    onNavigate?.call(3);
                  },
                ),
                _buildQuickAccessCard(
                  context,
                  icon: Icons.eco,
                  title: 'Carbon Tracking',
                  subtitle: 'Track sequestration',
                  color: AppTheme.lightGreen,
                  onTap: () {
                    // Navigate to carbon tracking tab
                    onNavigate?.call(4);
                  },
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Recent Activity Section
            Text(
              'Recent Activity',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: AppTheme.primaryBrown,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Recent Activity Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.history,
                          color: AppTheme.primaryBrown,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'No recent calculations',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Start by using the calculator to track your biochar production.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.charcoal.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Tips Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.lightGreen.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.lightGreen.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: AppTheme.forestGreen,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Tip of the Day',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: AppTheme.forestGreen,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Higher pyrolysis temperatures (400-600°C) generally produce biochar with better carbon sequestration properties.',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAccessCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.charcoal.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}