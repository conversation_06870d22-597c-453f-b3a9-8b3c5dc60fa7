import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/feedstock.dart';
import '../models/calculator_result.dart';
import '../services/feedstock_service.dart';
import '../services/calculator_service.dart';
import '../widgets/calculator_result_card.dart';

class CalculatorScreen extends StatefulWidget {
  const CalculatorScreen({super.key});

  @override
  State<CalculatorScreen> createState() => _CalculatorScreenState();
}

class _CalculatorScreenState extends State<CalculatorScreen> {
  final _formKey = GlobalKey<FormState>();
  final _feedstockAmountController = TextEditingController();
  final _moistureContentController = TextEditingController();
  final _temperatureController = TextEditingController();
  final _residenceTimeController = TextEditingController();
  
  List<Feedstock> _feedstocks = [];
  Feedstock? _selectedFeedstock;
  CalculatorResult? _result;
  bool _isLoading = false;
  Map<String, String> _validationErrors = {};

  @override
  void initState() {
    super.initState();
    _loadFeedstocks();
    // Set default values
    _temperatureController.text = '450';
    _residenceTimeController.text = '45';
    _moistureContentController.text = '10';
  }

  @override
  void dispose() {
    _feedstockAmountController.dispose();
    _moistureContentController.dispose();
    _temperatureController.dispose();
    _residenceTimeController.dispose();
    super.dispose();
  }

  Future<void> _loadFeedstocks() async {
    try {
      final feedstocks = await FeedstockService.loadFeedstocks();
      setState(() {
        _feedstocks = feedstocks;
        if (feedstocks.isNotEmpty) {
          _selectedFeedstock = feedstocks.first;
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading feedstocks: $e')),
        );
      }
    }
  }

  void _calculateBiochar() {
    if (!_formKey.currentState!.validate() || _selectedFeedstock == null) {
      return;
    }

    setState(() {
      _isLoading = true;
      _validationErrors = {};
    });

    try {
      final feedstockAmount = double.parse(_feedstockAmountController.text);
      final moistureContent = double.parse(_moistureContentController.text);
      final temperature = double.parse(_temperatureController.text);
      final residenceTime = double.parse(_residenceTimeController.text);

      // Validate inputs
      final errors = CalculatorService.validateInputs(
        feedstockAmount: feedstockAmount,
        moistureContent: moistureContent,
        pyrolysisTemperature: temperature,
        residenceTime: residenceTime,
      );

      if (errors.isNotEmpty) {
        setState(() {
          _validationErrors = errors;
          _isLoading = false;
        });
        return;
      }

      // Calculate results
      final result = CalculatorService.calculateBiocharYield(
        feedstock: _selectedFeedstock!,
        feedstockAmount: feedstockAmount,
        moistureContent: moistureContent,
        pyrolysisTemperature: temperature,
        residenceTime: residenceTime,
      );

      setState(() {
        _result = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Calculation error: $e')),
      );
    }
  }

  void _resetForm() {
    setState(() {
      _feedstockAmountController.clear();
      _moistureContentController.text = '10';
      _temperatureController.text = '450';
      _residenceTimeController.text = '45';
      _result = null;
      _validationErrors = {};
      if (_feedstocks.isNotEmpty) {
        _selectedFeedstock = _feedstocks.first;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Biochar Calculator'),
        backgroundColor: theme.colorScheme.surface,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _resetForm,
            tooltip: 'Reset Form',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.calculate,
                        size: 48,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Calculate Biochar Production',
                        style: theme.textTheme.headlineSmall,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Enter your feedstock details and pyrolysis parameters',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              
              // Feedstock Selection
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Feedstock Selection',
                        style: theme.textTheme.titleMedium,
                      ),
                      const SizedBox(height: 12),
                      DropdownButtonFormField<Feedstock>(
                        value: _selectedFeedstock,
                        decoration: const InputDecoration(
                          labelText: 'Select Feedstock Type',
                          prefixIcon: Icon(Icons.grass),
                        ),
                        items: _feedstocks.map((feedstock) {
                          return DropdownMenuItem(
                            value: feedstock,
                            child: Text(feedstock.name),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedFeedstock = value;
                          });
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'Please select a feedstock type';
                          }
                          return null;
                        },
                      ),
                      if (_selectedFeedstock != null) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            _selectedFeedstock!.description,
                            style: theme.textTheme.bodySmall,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              
              // Input Parameters
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Input Parameters',
                        style: theme.textTheme.titleMedium,
                      ),
                      const SizedBox(height: 12),
                      
                      // Feedstock Amount
                      TextFormField(
                        controller: _feedstockAmountController,
                        decoration: InputDecoration(
                          labelText: 'Feedstock Amount (kg)',
                          prefixIcon: const Icon(Icons.scale),
                          errorText: _validationErrors['feedstockAmount'],
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter feedstock amount';
                          }
                          final amount = double.tryParse(value);
                          if (amount == null || amount <= 0) {
                            return 'Please enter a valid amount';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Moisture Content
                      TextFormField(
                        controller: _moistureContentController,
                        decoration: InputDecoration(
                          labelText: 'Moisture Content (%)',
                          prefixIcon: const Icon(Icons.water_drop),
                          errorText: _validationErrors['moistureContent'],
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter moisture content';
                          }
                          final moisture = double.tryParse(value);
                          if (moisture == null || moisture < 0 || moisture > 95) {
                            return 'Moisture content must be between 0% and 95%';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Pyrolysis Temperature
                      TextFormField(
                        controller: _temperatureController,
                        decoration: InputDecoration(
                          labelText: 'Pyrolysis Temperature (°C)',
                          prefixIcon: const Icon(Icons.thermostat),
                          errorText: _validationErrors['pyrolysisTemperature'],
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter temperature';
                          }
                          final temp = double.tryParse(value);
                          if (temp == null || temp < 250 || temp > 800) {
                            return 'Temperature must be between 250°C and 800°C';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Residence Time
                      TextFormField(
                        controller: _residenceTimeController,
                        decoration: InputDecoration(
                          labelText: 'Residence Time (minutes)',
                          prefixIcon: const Icon(Icons.timer),
                          errorText: _validationErrors['residenceTime'],
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter residence time';
                          }
                          final time = double.tryParse(value);
                          if (time == null || time < 5 || time > 300) {
                            return 'Residence time must be between 5 and 300 minutes';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              
              // Calculate Button
              ElevatedButton(
                onPressed: _isLoading ? null : _calculateBiochar,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text(
                        'Calculate Biochar Production',
                        style: TextStyle(fontSize: 16),
                      ),
              ),
              
              // Results
              if (_result != null) ...[
                const SizedBox(height: 24),
                CalculatorResultCard(result: _result!),
              ],
            ],
          ),
        ),
      ),
    );
  }
}