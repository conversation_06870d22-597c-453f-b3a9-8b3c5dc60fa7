import 'package:flutter/material.dart';
import '../models/carbon_project.dart';
import '../services/carbon_project_service.dart';
import '../widgets/carbon_project_card.dart';
import 'carbon_project_detail_screen.dart';

class CarbonTrackingScreen extends StatefulWidget {
  const CarbonTrackingScreen({super.key});

  @override
  State<CarbonTrackingScreen> createState() => _CarbonTrackingScreenState();
}

class _CarbonTrackingScreenState extends State<CarbonTrackingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<CarbonProject> _projects = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedStatus = 'All';
  String _selectedType = 'All';

  final List<String> _statusOptions = [
    'All',
    'planning',
    'active',
    'completed',
    'verified',
  ];
  final List<String> _typeOptions = ['All'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final projects = await CarbonProjectService.loadProjects();
      final statistics = await CarbonProjectService.getProjectStatistics();
      final types = await CarbonProjectService.getProjectTypes();

      setState(() {
        _projects = projects;
        _statistics = statistics;
        _typeOptions.clear();
        _typeOptions.add('All');
        _typeOptions.addAll(types);
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error loading data: $e')));
      }
    }
  }

  List<CarbonProject> get _filteredProjects {
    return _projects.where((project) {
      final matchesSearch =
          _searchQuery.isEmpty ||
          project.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          project.description.toLowerCase().contains(
            _searchQuery.toLowerCase(),
          ) ||
          project.location.toLowerCase().contains(_searchQuery.toLowerCase());

      final matchesStatus =
          _selectedStatus == 'All' || project.status == _selectedStatus;
      final matchesType =
          _selectedType == 'All' || project.projectType == _selectedType;

      return matchesSearch && matchesStatus && matchesType;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Carbon Credit Tracking'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Overview'),
            Tab(icon: Icon(Icons.folder), text: 'Projects'),
            Tab(icon: Icon(Icons.calculate), text: 'Calculator'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildProjectsTab(),
                _buildCalculatorTab(),
              ],
            ),
      floatingActionButton: _tabController.index == 1
          ? FloatingActionButton(
              onPressed: () {
                // TODO: Implement add new project
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Add new project feature coming soon!'),
                  ),
                );
              },
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildOverviewTab() {
    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Portfolio Overview',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildStatisticsGrid(),
            const SizedBox(height: 24),
            Text(
              'Recent Projects',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ..._projects
                .take(3)
                .map(
                  (project) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: CarbonProjectCard(
                      project: project,
                      onTap: () => _navigateToProjectDetail(project),
                    ),
                  ),
                ),
          ],
        ),
      ),
    );
  }

  Widget _buildProjectsTab() {
    return Column(
      children: [
        // Search and Filter Bar
        Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              TextField(
                decoration: const InputDecoration(
                  hintText: 'Search projects...',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) => setState(() => _searchQuery = value),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedStatus,
                      isExpanded: true,
                      decoration: const InputDecoration(
                        labelText: 'Status',
                        border: OutlineInputBorder(),
                      ),
                      items: _statusOptions.map((status) {
                        return DropdownMenuItem(
                          value: status,
                          child: Text(
                            status == 'All' ? 'All Status' : status.toUpperCase(),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        );
                      }).toList(),
                      selectedItemBuilder: (context) {
                        return _statusOptions.map((status) {
                          final text = status == 'All' ? 'All Status' : status.toUpperCase();
                          return Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              text,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          );
                        }).toList();
                      },
                      onChanged: (value) => setState(() => _selectedStatus = value!),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedType,
                      isExpanded: true,
                      decoration: const InputDecoration(
                        labelText: 'Type',
                        border: OutlineInputBorder(),
                      ),
                      items: _typeOptions.map((type) {
                        return DropdownMenuItem(
                          value: type,
                          child: Text(
                            type == 'All' ? 'All Types' : type,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        );
                      }).toList(),
                      selectedItemBuilder: (context) {
                        return _typeOptions.map((type) {
                          final text = type == 'All' ? 'All Types' : type;
                          return Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              text,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          );
                        }).toList();
                      },
                      onChanged: (value) => setState(() => _selectedType = value!),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        // Projects List
        Expanded(
          child: RefreshIndicator(
            onRefresh: _loadData,
            child: _filteredProjects.isEmpty
                ? const Center(
                    child: Text(
                      'No projects found',
                      style: TextStyle(fontSize: 16),
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _filteredProjects.length,
                    itemBuilder: (context, index) {
                      final project = _filteredProjects[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: CarbonProjectCard(
                          project: project,
                          onTap: () => _navigateToProjectDetail(project),
                        ),
                      );
                    },
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildCalculatorTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Carbon Credit Calculator',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildCreditCalculator(),
          const SizedBox(height: 24),
          Text(
            'Revenue Estimator',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildRevenueEstimator(),
        ],
      ),
    );
  }

  Widget _buildStatisticsGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: 1.2,
      children: [
        _buildStatCard(
          'Total Projects',
          '${_statistics['totalProjects'] ?? 0}',
          Icons.folder,
          Colors.blue,
        ),
        _buildStatCard(
          'Active Projects',
          '${_statistics['activeProjects'] ?? 0}',
          Icons.play_circle,
          Colors.green,
        ),
        _buildStatCard(
          'Carbon Sequestered',
          '${(_statistics['totalCarbonSequestered'] ?? 0.0).toStringAsFixed(0)} t',
          Icons.eco,
          Colors.green,
        ),
        _buildStatCard(
          'Credits Generated',
          '${(_statistics['totalCreditsGenerated'] ?? 0.0).toStringAsFixed(0)}',
          Icons.stars,
          Colors.orange,
        ),
        _buildStatCard(
          'Credits Sold',
          '${(_statistics['totalCreditsSold'] ?? 0.0).toStringAsFixed(0)}',
          Icons.sell,
          Colors.purple,
        ),
        _buildStatCard(
          'Total Revenue',
          '\$${(_statistics['totalRevenue'] ?? 0.0).toStringAsFixed(0)}k',
          Icons.attach_money,
          Colors.green,
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreditCalculator() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Calculate Potential Credits',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'Enter biochar production details to estimate carbon credits:',
            ),
            const SizedBox(height: 12),
            // TODO: Add calculator form fields
            const TextField(
              decoration: InputDecoration(
                labelText: 'Biochar Amount (tons)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 12),
            const TextField(
              decoration: InputDecoration(
                labelText: 'Carbon Content (%)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // TODO: Implement calculation
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Calculation feature coming soon!'),
                  ),
                );
              },
              child: const Text('Calculate Credits'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRevenueEstimator() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Estimate Project Revenue',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text('Estimate potential revenue from carbon credit sales:'),
            const SizedBox(height: 12),
            // TODO: Add revenue estimator form fields
            const TextField(
              decoration: InputDecoration(
                labelText: 'Credits Generated',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 12),
            const TextField(
              decoration: InputDecoration(
                labelText: 'Credit Price (\$/ton CO2e)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // TODO: Implement revenue estimation
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Revenue estimation feature coming soon!'),
                  ),
                );
              },
              child: const Text('Estimate Revenue'),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToProjectDetail(CarbonProject project) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CarbonProjectDetailScreen(project: project),
      ),
    );
  }
}
