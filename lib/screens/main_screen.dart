import 'package:flutter/material.dart';
import 'home_screen.dart';
import 'knowledge_hub_screen.dart';
import 'calculator_screen.dart';
import 'feedstock_screen.dart';
import 'carbon_tracking_screen.dart';
import '../widgets/admob_banner.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  late final List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _screens = [
      HomeScreen(onNavigate: (index) {
        setState(() {
          _currentIndex = index;
        });
      }),
      const KnowledgeHubScreen(),
      const CalculatorScreen(),
      const FeedstockScreen(),
      const CarbonTrackingScreen(),
    ];
  }

  final List<BottomNavigationBarItem> _navigationItems = [
    const BottomNavigationBarItem(
      icon: Icon(Icons.home_outlined),
      activeIcon: Icon(Icons.home),
      label: 'Home',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.library_books_outlined),
      activeIcon: Icon(Icons.library_books),
      label: 'Knowledge',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.calculate_outlined),
      activeIcon: Icon(Icons.calculate),
      label: 'Calculator',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.grass_outlined),
      activeIcon: Icon(Icons.grass),
      label: 'Feedstock',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.eco_outlined),
      activeIcon: Icon(Icons.eco),
      label: 'Carbon',
    ),
  ];

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('About Biochar App'),
          content: const SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Biochar Production Assistant',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                SizedBox(height: 8),
                Text(
                  'A comprehensive mobile application designed to help users learn about, calculate, and track biochar production for sustainable agriculture and carbon sequestration.',
                ),
                SizedBox(height: 12),
                Text(
                  'Features:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text('• Educational content and articles'),
                Text('• Biochar yield calculator'),
                Text('• Feedstock database'),
                Text('• Carbon credit tracking'),
                SizedBox(height: 12),
                Text(
                  'Version 1.0.0',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  void _showRatingDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Rate This App'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Enjoying the Biochar App?'),
              SizedBox(height: 8),
              Text(
                'Please consider rating us on the app store to help others discover this app!',
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Maybe Later'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // In a real app, this would open the app store
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      'Thank you for your interest! App store integration coming soon.',
                    ),
                  ),
                );
              },
              child: const Text('Rate Now'),
            ),
          ],
        );
      },
    );
  }

  void _showSourcesDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Sources & References'),
          content: const SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Scientific Foundation',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                SizedBox(height: 8),
                Text(
                  'This app is based on peer-reviewed research and established scientific methods for biochar production and carbon sequestration.',
                ),
                SizedBox(height: 12),
                Text(
                  'Key References:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 4),
                Text('• International Biochar Initiative (IBI) Guidelines'),
                Text('• IPCC Guidelines for Carbon Accounting'),
                Text('• Lehmann & Joseph (2015) - Biochar for Environmental Management'),
                Text('• Woolf et al. (2010) - Nature Communications'),
                Text('• Sohi et al. (2010) - Carbon Sequestration in Soils'),
                SizedBox(height: 12),
                Text(
                  'Calculation Methods:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 4),
                Text('• Yield calculations based on feedstock carbon content'),
                Text('• Carbon sequestration rates from peer-reviewed studies'),
                Text('• Temperature and residence time correlations'),
                Text('• Mass balance equations for pyrolysis processes'),
                SizedBox(height: 12),
                Text(
                  'Standards Compliance:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 4),
                Text('• Verra VCS (Verified Carbon Standard)'),
                Text('• Gold Standard for carbon credits'),
                Text('• ISO 14855 biodegradability standards'),
                Text('• ASTM D1762 biochar characterization'),
                SizedBox(height: 12),
                Text(
                  'Data Sources:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 4),
                Text('• FAO Agricultural Statistics'),
                Text('• USDA Biomass Feedstock Database'),
                Text('• European Biochar Certificate (EBC)'),
                Text('• Academic research publications (2010-2024)'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  void _showTabExplanation(String tabName, String explanation) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('$tabName Tab'),
          content: Text(explanation),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Got it'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Biochar App'),
        leading: PopupMenuButton<String>(
          icon: const Icon(Icons.menu),
          onSelected: (String value) {
            switch (value) {
              case 'home_info':
                _showTabExplanation(
                  'Home',
                  'The Home tab provides a welcome overview and quick access to all main features of the app. Start here to navigate to different sections.',
                );
                break;
              case 'knowledge_info':
                _showTabExplanation(
                  'Knowledge Hub',
                  'Access educational articles, research papers, and comprehensive information about biochar production, benefits, and applications.',
                );
                break;
              case 'calculator_info':
                _showTabExplanation(
                  'Calculator',
                  'Calculate biochar yield from different feedstock materials. Input your feedstock type and quantity to estimate production output.',
                );
                break;
              case 'feedstock_info':
                _showTabExplanation(
                  'Feedstock Database',
                  'Browse and search through various organic materials suitable for biochar production, with detailed information about each type.',
                );
                break;
              case 'carbon_info':
                _showTabExplanation(
                  'Carbon Tracking',
                  'Track carbon sequestration projects and manage carbon credit calculations.\n\n'
                  'Setting Up Projects:\n'
                  '• Create new biochar projects with location and scale details\n'
                  '• Define project parameters like feedstock type and production capacity\n'
                  '• Set baseline measurements for carbon sequestration tracking\n'
                  '• Monitor project progress with status updates (Active, Verified, etc.)\n\n'
                  'Using the Calculator:\n'
                  '• Input your biochar production data (amount, feedstock type)\n'
                  '• Calculate carbon credits based on sequestration rates\n'
                  '• Track cumulative carbon impact across all projects\n'
                  '• Generate reports for carbon credit verification\n\n'
                  'The system automatically calculates carbon sequestration potential based on biochar properties and helps you manage multiple projects for maximum environmental impact.',
                );
                break;
              case 'about':
                _showAboutDialog();
                break;
              case 'sources':
                _showSourcesDialog();
                break;
              case 'rate':
                _showRatingDialog();
                break;
            }
          },
          itemBuilder: (BuildContext context) => [
            const PopupMenuItem<String>(
              value: 'home_info',
              child: ListTile(
                leading: Icon(Icons.home_outlined),
                title: Text('Home Tab'),
                subtitle: Text('Overview and navigation'),
              ),
            ),
            const PopupMenuItem<String>(
              value: 'knowledge_info',
              child: ListTile(
                leading: Icon(Icons.library_books_outlined),
                title: Text('Knowledge Hub'),
                subtitle: Text('Educational content'),
              ),
            ),
            const PopupMenuItem<String>(
              value: 'calculator_info',
              child: ListTile(
                leading: Icon(Icons.calculate_outlined),
                title: Text('Calculator'),
                subtitle: Text('Biochar yield calculations'),
              ),
            ),
            const PopupMenuItem<String>(
              value: 'feedstock_info',
              child: ListTile(
                leading: Icon(Icons.grass_outlined),
                title: Text('Feedstock Database'),
                subtitle: Text('Material information'),
              ),
            ),
            const PopupMenuItem<String>(
              value: 'carbon_info',
              child: ListTile(
                leading: Icon(Icons.eco_outlined),
                title: Text('Carbon Tracking'),
                subtitle: Text('Project monitoring'),
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem<String>(
              value: 'sources',
              child: ListTile(
                leading: Icon(Icons.science_outlined),
                title: Text('Sources & References'),
                subtitle: Text('Scientific foundation'),
              ),
            ),
            const PopupMenuItem<String>(
              value: 'about',
              child: ListTile(
                leading: Icon(Icons.info_outline),
                title: Text('About Us'),
                subtitle: Text('App information'),
              ),
            ),
            const PopupMenuItem<String>(
              value: 'rate',
              child: ListTile(
                leading: Icon(Icons.star_outline),
                title: Text('Rate This App'),
                subtitle: Text('Share your feedback'),
              ),
            ),
          ],
        ),
      ),
      body: AdMobBannerWrapper(
        child: IndexedStack(index: _currentIndex, children: _screens),
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: _navigationItems,
      ),
    );
  }
}
