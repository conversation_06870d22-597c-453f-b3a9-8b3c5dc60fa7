import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import '../models/article.dart';

class ArticleReaderScreen extends StatelessWidget {
  final Article article;

  const ArticleReaderScreen({super.key, required this.article});

  void _shareArticle() {
    final shareText = '''
${article.title}

${article.summary}

Read more about biochar and sustainable agriculture in the Biochar App!
''';
    
    Share.share(
      shareText,
      subject: 'Biochar Article: ${article.title}',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          article.title,
          style: const TextStyle(fontSize: 16),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareArticle(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Category badge
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                article.category,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Title
            Text(
              article.title,
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                height: 1.2,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Summary
            Text(
              article.summary,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                height: 1.5,
                fontStyle: FontStyle.italic,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Read time
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: Colors.grey[500],
                ),
                const SizedBox(width: 4),
                Text(
                  '${article.readTimeMinutes} min read',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 24),
            
            // Content
            _buildContent(context),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    // Convert content sections to text and split by paragraphs
    final contentText = article.content.map((section) => section.text).join('\n\n');
    final paragraphs = contentText.split('\n\n');
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: paragraphs.map((paragraph) {
        if (paragraph.trim().isEmpty) {
          return const SizedBox(height: 16);
        }
        
        // Check if it's a heading (starts with #)
        if (paragraph.startsWith('#')) {
          return _buildHeading(paragraph);
        }
        
        // Check if it's a bullet point (starts with -)
        if (paragraph.startsWith('-')) {
          return _buildBulletPoint(paragraph);
        }
        
        // Regular paragraph
        return _buildParagraph(paragraph);
      }).toList(),
    );
  }

  Widget _buildHeading(String text) {
    final cleanText = text.replaceAll('#', '').trim();
    final level = text.indexOf(' ');
    
    double fontSize = 24;
    if (level > 1) fontSize = 20;
    if (level > 2) fontSize = 18;
    
    return Padding(
      padding: const EdgeInsets.only(top: 24, bottom: 12),
      child: Text(
        cleanText,
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          height: 1.3,
        ),
      ),
    );
  }

  Widget _buildBulletPoint(String text) {
    final cleanText = text.substring(1).trim();
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8, left: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '•',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              cleanText,
              style: const TextStyle(
                fontSize: 16,
                height: 1.6,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildParagraph(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 16,
          height: 1.6,
        ),
        textAlign: TextAlign.justify,
      ),
    );
  }
}