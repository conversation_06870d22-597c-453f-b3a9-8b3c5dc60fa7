class Feedstock {
  final String id;
  final String name;
  final String description;
  final String type;
  final String category;
  final String thumbnail;
  final YieldRange yieldRange;
  final String pyrolysisTemp;
  final String considerations;
  final List<String> advantages;
  final List<String> disadvantages;
  final double sustainabilityScore;
  final double cost;
  final String costUnit;
  final List<String> processingRequirements;
  final double bulkDensity;
  final double energyContent;
  final double biocharYield;
  final double pyrolysisTemperature;
  final double moistureContent;
  final double ashContent;
  final double volatileMatter;
  final double fixedCarbon;
  final String availability;
  final String seasonality;
  final double carbonContent;

  Feedstock({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.category,
    required this.thumbnail,
    required this.yieldRange,
    required this.pyrolysisTemp,
    required this.considerations,
    required this.advantages,
    required this.disadvantages,
    required this.sustainabilityScore,
    required this.cost,
    required this.costUnit,
    required this.processingRequirements,
    required this.bulkDensity,
    required this.energyContent,
    required this.biocharYield,
    required this.pyrolysisTemperature,
    required this.moistureContent,
    required this.ashContent,
    required this.volatileMatter,
    required this.fixedCarbon,
    required this.availability,
    required this.seasonality,
    required this.carbonContent,
  });

  factory Feedstock.fromJson(Map<String, dynamic> json) {
    return Feedstock(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      category: json['category'] as String,
      thumbnail: json['thumbnail'] ?? '',
      yieldRange: YieldRange.fromJson(json['yield_range'] ?? {'min': 0.0, 'max': 0.0}),
      pyrolysisTemp: json['pyrolysis_temp']?.toString() ?? json['pyrolysisTemp']?.toString() ?? '450',
      considerations: json['considerations'] ?? '',
      advantages: List<String>.from(json['advantages'] ?? []),
      disadvantages: List<String>.from(json['disadvantages'] ?? []),
      sustainabilityScore: (json['sustainabilityScore'] ?? 0.0).toDouble(),
      cost: (json['cost'] ?? 0.0).toDouble(),
      costUnit: json['costUnit'] ?? 'per ton',
      processingRequirements: List<String>.from(json['processingRequirements'] ?? []),
      bulkDensity: (json['bulkDensity'] ?? 0.0).toDouble(),
      energyContent: (json['energyContent'] ?? 0.0).toDouble(),
      biocharYield: (json['biocharYield'] ?? 0.0).toDouble(),
      pyrolysisTemperature: (json['pyrolysisTemp'] ?? 0.0).toDouble(),
      moistureContent: (json['moistureContent'] ?? 0.0).toDouble(),
      ashContent: (json['ashContent'] ?? 0.0).toDouble(),
      volatileMatter: (json['volatileContent'] ?? 0.0).toDouble(),
      fixedCarbon: (json['fixedCarbon'] ?? 0.0).toDouble(),
      availability: json['availability'] ?? '',
      seasonality: json['seasonality'] ?? '',
      carbonContent: (json['carbonContent'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type,
      'category': category,
      'thumbnail': thumbnail,
      'yield_range': yieldRange.toJson(),
      'pyrolysis_temp': pyrolysisTemp,
      'considerations': considerations,
      'advantages': advantages,
      'disadvantages': disadvantages,
      'sustainabilityScore': sustainabilityScore,
      'cost': cost,
      'costUnit': costUnit,
      'processingRequirements': processingRequirements,
      'bulkDensity': bulkDensity,
      'energyContent': energyContent,
      'biocharYield': biocharYield,
      'pyrolysisTemp': pyrolysisTemperature,
      'moistureContent': moistureContent,
      'ashContent': ashContent,
      'volatileContent': volatileMatter,
      'fixedCarbon': fixedCarbon,
      'availability': availability,
      'seasonality': seasonality,
      'carbonContent': carbonContent,
    };
  }
}

class YieldRange {
  final double min;
  final double max;

  YieldRange({
    required this.min,
    required this.max,
  });

  factory YieldRange.fromJson(Map<String, dynamic> json) {
    return YieldRange(
      min: (json['min'] as num).toDouble(),
      max: (json['max'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'min': min,
      'max': max,
    };
  }
}