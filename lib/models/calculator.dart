class CalculatorInput {
  final String feedstockId;
  final double weightKg;
  final String tempRange;

  CalculatorInput({
    required this.feedstockId,
    required this.weightKg,
    required this.tempRange,
  });

  factory CalculatorInput.fromJson(Map<String, dynamic> json) {
    return CalculatorInput(
      feedstockId: json['feedstock_id'] as String,
      weightKg: (json['weight_kg'] as num).toDouble(),
      tempRange: json['temp_range'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'feedstock_id': feedstockId,
      'weight_kg': weightKg,
      'temp_range': tempRange,
    };
  }
}

class CalculatorOutput {
  final double yieldKg;
  final double carbonStorageKgCo2Eq;

  CalculatorOutput({
    required this.yieldKg,
    required this.carbonStorageKgCo2Eq,
  });

  factory CalculatorOutput.fromJson(Map<String, dynamic> json) {
    return CalculatorOutput(
      yieldKg: (json['yield_kg'] as num).toDouble(),
      carbonStorageKgCo2Eq: (json['carbon_storage_kg_co2_eq'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'yield_kg': yieldKg,
      'carbon_storage_kg_co2_eq': carbonStorageKgCo2Eq,
    };
  }
}

class CalculatorHistory {
  final String id;
  final String feedstockId;
  final double weightKg;
  final String tempRange;
  final double yieldKg;
  final double carbonStorage;
  final DateTime createdAt;

  CalculatorHistory({
    required this.id,
    required this.feedstockId,
    required this.weightKg,
    required this.tempRange,
    required this.yieldKg,
    required this.carbonStorage,
    required this.createdAt,
  });

  factory CalculatorHistory.fromJson(Map<String, dynamic> json) {
    return CalculatorHistory(
      id: json['id'] as String,
      feedstockId: json['feedstock_id'] as String,
      weightKg: (json['weight_kg'] as num).toDouble(),
      tempRange: json['temp_range'] as String,
      yieldKg: (json['yield_kg'] as num).toDouble(),
      carbonStorage: (json['carbon_storage'] as num).toDouble(),
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'feedstock_id': feedstockId,
      'weight_kg': weightKg,
      'temp_range': tempRange,
      'yield_kg': yieldKg,
      'carbon_storage': carbonStorage,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory CalculatorHistory.fromMap(Map<String, dynamic> map) {
    return CalculatorHistory(
      id: map['id'],
      feedstockId: map['feedstock_id'],
      weightKg: map['weight_kg'],
      tempRange: map['temp_range'],
      yieldKg: map['yield_kg'],
      carbonStorage: map['carbon_storage'],
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'feedstock_id': feedstockId,
      'weight_kg': weightKg,
      'temp_range': tempRange,
      'yield_kg': yieldKg,
      'carbon_storage': carbonStorage,
      'created_at': createdAt.toIso8601String(),
    };
  }
}