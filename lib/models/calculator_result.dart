class CalculatorResult {
  final String id;
  final String feedstockId;
  final double feedstockAmount;
  final double biocharYield;
  final double carbonContent;
  final double carbonSequestered;
  final double energyContent;
  final double carbonEfficiency;
  final String feedstockType;
  final double pyrolysisTemperature;
  final double residenceTime;
  final double moistureContent;
  final Map<String, double> economicValue;
  final DateTime calculatedAt;
  final Map<String, dynamic> parameters;

  CalculatorResult({
    required this.id,
    required this.feedstockId,
    required this.feedstockAmount,
    required this.biocharYield,
    required this.carbonContent,
    required this.carbonSequestered,
    required this.energyContent,
    required this.carbonEfficiency,
    required this.feedstockType,
    required this.pyrolysisTemperature,
    required this.residenceTime,
    required this.moistureContent,
    required this.economicValue,
    required this.calculatedAt,
    required this.parameters,
  });

  factory CalculatorResult.fromJson(Map<String, dynamic> json) {
    return CalculatorResult(
      id: json['id'] as String,
      feedstockId: json['feedstock_id'] as String,
      feedstockAmount: (json['feedstock_amount'] as num).toDouble(),
      biocharYield: (json['biochar_yield'] as num).toDouble(),
      carbonContent: (json['carbon_content'] as num).toDouble(),
      carbonSequestered: (json['carbon_sequestered'] as num).toDouble(),
      energyContent: (json['energy_content'] as num).toDouble(),
      carbonEfficiency: (json['carbon_efficiency'] ?? 0.0).toDouble(),
      feedstockType: json['feedstock_type'] ?? '',
      pyrolysisTemperature: (json['pyrolysis_temperature'] ?? 0.0).toDouble(),
      residenceTime: (json['residence_time'] ?? 0.0).toDouble(),
      moistureContent: (json['moisture_content'] ?? 0.0).toDouble(),
      economicValue: Map<String, double>.from(json['economic_value'] as Map),
      calculatedAt: DateTime.parse(json['calculated_at'] as String),
      parameters: json['parameters'] as Map<String, dynamic>,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'feedstock_id': feedstockId,
      'feedstock_amount': feedstockAmount,
      'biochar_yield': biocharYield,
      'carbon_content': carbonContent,
      'carbon_sequestered': carbonSequestered,
      'energy_content': energyContent,
      'carbon_efficiency': carbonEfficiency,
      'feedstock_type': feedstockType,
      'pyrolysis_temperature': pyrolysisTemperature,
      'residence_time': residenceTime,
      'moisture_content': moistureContent,
      'economic_value': economicValue,
      'calculated_at': calculatedAt.toIso8601String(),
      'parameters': parameters,
    };
  }

  double get co2EquivalentReduction => carbonSequestered * 3.67; // CO2 = C * 3.67
  
  double get co2Equivalent => carbonSequestered * 3.67; // CO2 = C * 3.67
  
  double get yieldPercentage => (biocharYield / feedstockAmount) * 100;
  
  double get massEfficiency => (biocharYield / feedstockAmount) * 100;
  
  double get dryFeedstockAmount => feedstockAmount * (1 - moistureContent / 100);
  
  String get formattedDate => '${calculatedAt.day}/${calculatedAt.month}/${calculatedAt.year}';
}