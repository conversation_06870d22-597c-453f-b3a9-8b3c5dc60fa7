class UserPreferences {
  final int? id;
  final String themeMode;
  final String preferredUnits;
  final String? defaultFeedstock;
  final bool showTutorials;
  final bool enableNotifications;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserPreferences({
    this.id,
    required this.themeMode,
    required this.preferredUnits,
    this.defaultFeedstock,
    required this.showTutorials,
    required this.enableNotifications,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      id: json['id'],
      themeMode: json['theme_mode'],
      preferredUnits: json['preferred_units'],
      defaultFeedstock: json['default_feedstock'],
      showTutorials: json['show_tutorials'] == 1,
      enableNotifications: json['enable_notifications'] == 1,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'theme_mode': themeMode,
      'preferred_units': preferredUnits,
      'default_feedstock': defaultFeedstock,
      'show_tutorials': showTutorials ? 1 : 0,
      'enable_notifications': enableNotifications ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory UserPreferences.fromMap(Map<String, dynamic> map) {
    return UserPreferences(
      id: map['id'],
      themeMode: map['theme_mode'],
      preferredUnits: map['preferred_units'],
      defaultFeedstock: map['default_feedstock'],
      showTutorials: map['show_tutorials'] == 1,
      enableNotifications: map['enable_notifications'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'theme_mode': themeMode,
      'preferred_units': preferredUnits,
      'default_feedstock': defaultFeedstock,
      'show_tutorials': showTutorials ? 1 : 0,
      'enable_notifications': enableNotifications ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  UserPreferences copyWith({
    int? id,
    String? themeMode,
    String? preferredUnits,
    String? defaultFeedstock,
    bool? showTutorials,
    bool? enableNotifications,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserPreferences(
      id: id ?? this.id,
      themeMode: themeMode ?? this.themeMode,
      preferredUnits: preferredUnits ?? this.preferredUnits,
      defaultFeedstock: defaultFeedstock ?? this.defaultFeedstock,
      showTutorials: showTutorials ?? this.showTutorials,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}