class Article {
  final String id;
  final String category;
  final String title;
  final String summary;
  final int readTimeMinutes;
  final List<ContentSection> content;
  final String lastUpdated;

  Article({
    required this.id,
    required this.category,
    required this.title,
    required this.summary,
    required this.readTimeMinutes,
    required this.content,
    required this.lastUpdated,
  });

  factory Article.fromJson(Map<String, dynamic> json) {
    return Article(
      id: json['id'] as String,
      category: json['category'] as String,
      title: json['title'] as String,
      summary: json['summary'] as String,
      readTimeMinutes: json['readTimeMinutes'] as int,
      content: (json['content'] as List<dynamic>)
          .map((item) => ContentSection.fromJson(item as Map<String, dynamic>))
          .toList(),
      lastUpdated: json['last_updated'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'title': title,
      'summary': summary,
      'readTimeMinutes': readTimeMinutes,
      'content': content.map((section) => section.toJson()).toList(),
      'last_updated': lastUpdated,
    };
  }
}

class ContentSection {
  final String section;
  final String text;

  ContentSection({
    required this.section,
    required this.text,
  });

  factory ContentSection.fromJson(Map<String, dynamic> json) {
    return ContentSection(
      section: json['section'] as String,
      text: json['text'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'section': section,
      'text': text,
    };
  }
  
  // Getters for compatibility
  String get title => section;
  String get content => text;
}