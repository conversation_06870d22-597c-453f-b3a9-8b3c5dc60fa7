class CarbonProject {
  final String id;
  final String name;
  final String description;
  final String location;
  final String projectType;
  final String status; // 'planning', 'active', 'completed', 'verified'
  final DateTime startDate;
  final DateTime? endDate;
  final double totalBiocharProduced; // in tons
  final double carbonSequestered; // in tons CO2e
  final double creditsGenerated;
  final double creditsVerified;
  final double creditsSold;
  final double creditPrice; // per ton CO2e
  final String verificationStandard; // 'VCS', 'Gold Standard', 'CDM', etc.
  final String? verificationBody;
  final DateTime? lastVerificationDate;
  final List<String> feedstockTypes;
  final Map<String, dynamic> additionalData;

  CarbonProject({
    required this.id,
    required this.name,
    required this.description,
    required this.location,
    required this.projectType,
    required this.status,
    required this.startDate,
    this.endDate,
    required this.totalBiocharProduced,
    required this.carbonSequestered,
    required this.creditsGenerated,
    this.creditsVerified = 0.0,
    this.creditsSold = 0.0,
    this.creditPrice = 0.0,
    required this.verificationStandard,
    this.verificationBody,
    this.lastVerificationDate,
    required this.feedstockTypes,
    this.additionalData = const {},
  });

  factory CarbonProject.fromJson(Map<String, dynamic> json) {
    return CarbonProject(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      location: json['location'] as String,
      projectType: json['projectType'] as String,
      status: json['status'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate'] as String) : null,
      totalBiocharProduced: (json['totalBiocharProduced'] as num).toDouble(),
      carbonSequestered: (json['carbonSequestered'] as num).toDouble(),
      creditsGenerated: (json['creditsGenerated'] as num).toDouble(),
      creditsVerified: (json['creditsVerified'] as num?)?.toDouble() ?? 0.0,
      creditsSold: (json['creditsSold'] as num?)?.toDouble() ?? 0.0,
      creditPrice: (json['creditPrice'] as num?)?.toDouble() ?? 0.0,
      verificationStandard: json['verificationStandard'] as String,
      verificationBody: json['verificationBody'] as String?,
      lastVerificationDate: json['lastVerificationDate'] != null 
          ? DateTime.parse(json['lastVerificationDate'] as String) 
          : null,
      feedstockTypes: List<String>.from(json['feedstockTypes'] as List),
      additionalData: Map<String, dynamic>.from(json['additionalData'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'location': location,
      'projectType': projectType,
      'status': status,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'totalBiocharProduced': totalBiocharProduced,
      'carbonSequestered': carbonSequestered,
      'creditsGenerated': creditsGenerated,
      'creditsVerified': creditsVerified,
      'creditsSold': creditsSold,
      'creditPrice': creditPrice,
      'verificationStandard': verificationStandard,
      'verificationBody': verificationBody,
      'lastVerificationDate': lastVerificationDate?.toIso8601String(),
      'feedstockTypes': feedstockTypes,
      'additionalData': additionalData,
    };
  }

  // Helper methods
  double get creditsAvailable => creditsVerified - creditsSold;
  double get availableCredits => creditsAvailable; // Alias for consistency
  
  double get totalRevenue => creditsSold * creditPrice;
  double get projectRevenue => totalRevenue; // Alias for consistency
  
  double get potentialRevenue => creditsAvailable * creditPrice;
  
  // Alias for biochar produced to match widget expectations
  double get biocharProduced => totalBiocharProduced;
  
  // Alias for project ID to match widget expectations
  String get projectId => id;
  
  bool get isActive => status == 'active';
  
  bool get isCompleted => status == 'completed';
  
  bool get isVerified => creditsVerified > 0;
  
  double get verificationProgress => creditsGenerated > 0 ? creditsVerified / creditsGenerated : 0.0;
  
  double get salesProgress => creditsVerified > 0 ? creditsSold / creditsVerified : 0.0;
  
  int get projectDurationDays {
    final end = endDate ?? DateTime.now();
    return end.difference(startDate).inDays;
  }
  
  // Alias for project duration
  int get projectDuration => projectDurationDays;
  
  double get carbonSequestrationRate => projectDurationDays > 0 
      ? carbonSequestered / projectDurationDays * 365 
      : 0.0; // tons CO2e per year
}