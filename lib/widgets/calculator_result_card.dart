import 'package:flutter/material.dart';
import '../models/calculator_result.dart';
import '../services/calculator_service.dart';

class CalculatorResultCard extends StatelessWidget {
  final CalculatorResult result;

  const CalculatorResultCard({
    super.key,
    required this.result,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final economicValue = CalculatorService.calculateEconomicValue(result);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Calculation Results',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Production Results
            _buildSectionHeader(context, 'Production Results', Icons.factory),
            const SizedBox(height: 8),
            _buildResultGrid(context, [
              _ResultItem(
                label: 'Biochar Yield',
                value: '${result.biocharYield.toStringAsFixed(2)} kg',
                icon: Icons.eco,
                color: theme.colorScheme.primary,
              ),
              _ResultItem(
                label: 'Mass Efficiency',
                value: '${result.massEfficiency.toStringAsFixed(1)}%',
                icon: Icons.percent,
                color: theme.colorScheme.secondary,
              ),
              _ResultItem(
                label: 'Dry Feedstock Used',
                value: '${result.dryFeedstockAmount.toStringAsFixed(2)} kg',
                icon: Icons.grass,
                color: Colors.brown,
              ),
              _ResultItem(
                label: 'Energy Content',
                value: '${result.energyContent.toStringAsFixed(1)} MJ',
                icon: Icons.bolt,
                color: Colors.orange,
              ),
            ]),
            const SizedBox(height: 16),

            // Carbon Impact
            _buildSectionHeader(context, 'Carbon Impact', Icons.co2),
            const SizedBox(height: 8),
            _buildResultGrid(context, [
              _ResultItem(
                label: 'Carbon Sequestered',
                value: '${result.carbonSequestered.toStringAsFixed(2)} kg C',
                icon: Icons.forest,
                color: Colors.green,
              ),
              _ResultItem(
                label: 'CO₂ Equivalent',
                value: '${result.co2Equivalent.toStringAsFixed(2)} kg CO₂',
                icon: Icons.cloud,
                color: Colors.blue,
              ),
              _ResultItem(
                label: 'Carbon Efficiency',
                value: '${result.carbonEfficiency.toStringAsFixed(1)}%',
                icon: Icons.trending_up,
                color: Colors.teal,
              ),
            ]),
            const SizedBox(height: 16),

            // Economic Value
            _buildSectionHeader(context, 'Economic Value (Estimated)', Icons.attach_money),
            const SizedBox(height: 8),
            _buildResultGrid(context, [
              _ResultItem(
                label: 'Biochar Value',
                value: '\$${economicValue['biocharValue']!.toStringAsFixed(2)}',
                icon: Icons.monetization_on,
                color: Colors.green.shade700,
              ),
              _ResultItem(
                label: 'Carbon Credits',
                value: '\$${economicValue['carbonCreditValue']!.toStringAsFixed(2)}',
                icon: Icons.eco,
                color: Colors.blue.shade700,
              ),
              _ResultItem(
                label: 'Total Value',
                value: '\$${economicValue['totalValue']!.toStringAsFixed(2)}',
                icon: Icons.account_balance_wallet,
                color: Colors.purple.shade700,
              ),
            ]),
            const SizedBox(height: 16),

            // Process Parameters Summary
            _buildSectionHeader(context, 'Process Parameters', Icons.settings),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  _buildParameterRow(
                    context,
                    'Feedstock Type',
                    result.feedstockType,
                  ),
                  _buildParameterRow(
                    context,
                    'Temperature',
                    '${result.pyrolysisTemperature.toStringAsFixed(0)}°C',
                  ),
                  _buildParameterRow(
                    context,
                    'Residence Time',
                    '${result.residenceTime.toStringAsFixed(0)} min',
                  ),
                  _buildParameterRow(
                    context,
                    'Moisture Content',
                    '${result.moistureContent.toStringAsFixed(1)}%',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Disclaimer
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.amber.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.amber.shade700,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'These calculations are estimates based on typical values. Actual results may vary depending on feedstock quality, equipment efficiency, and process conditions.',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.amber.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title, IconData icon) {
    final theme = Theme.of(context);
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildResultGrid(BuildContext context, List<_ResultItem> items) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 2.5,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildResultItem(context, item);
      },
    );
  }

  Widget _buildResultItem(BuildContext context, _ResultItem item) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: item.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: item.color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Icon(
                item.icon,
                size: 16,
                color: item.color,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  item.label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            item.value,
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: item.color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildParameterRow(BuildContext context, String label, String value) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              label,
              style: theme.textTheme.bodyMedium,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}

class _ResultItem {
  final String label;
  final String value;
  final IconData icon;
  final Color color;

  const _ResultItem({
    required this.label,
    required this.value,
    required this.icon,
    required this.color,
  });
}