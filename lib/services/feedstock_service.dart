import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/feedstock.dart';

class FeedstockService {
  static List<Feedstock>? _cachedFeedstocks;
  static const String _feedstockDataPath = 'assets/data/feedstocks.json';

  /// Load feedstocks from JSON asset file
  static Future<List<Feedstock>> loadFeedstocks() async {
    if (_cachedFeedstocks != null) {
      return _cachedFeedstocks!;
    }

    try {
      final String jsonString = await rootBundle.loadString(_feedstockDataPath);
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      
      final List<dynamic> feedstockList = jsonData['feedstocks'] ?? [];
      
      _cachedFeedstocks = feedstockList
          .map((json) => Feedstock.fromJson(json as Map<String, dynamic>))
          .toList();
      
      return _cachedFeedstocks!;
    } catch (e) {
      throw Exception('Failed to load feedstocks: $e');
    }
  }

  /// Get feedstock by ID
  static Future<Feedstock?> getFeedstockById(String id) async {
    final feedstocks = await loadFeedstocks();
    try {
      return feedstocks.firstWhere((feedstock) => feedstock.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get feedstocks by category
  static Future<List<Feedstock>> getFeedstocksByCategory(String category) async {
    final feedstocks = await loadFeedstocks();
    return feedstocks
        .where((feedstock) => feedstock.category.toLowerCase() == category.toLowerCase())
        .toList();
  }

  /// Search feedstocks by name or description
  static Future<List<Feedstock>> searchFeedstocks(String query) async {
    if (query.isEmpty) {
      return await loadFeedstocks();
    }

    final feedstocks = await loadFeedstocks();
    final lowercaseQuery = query.toLowerCase();
    
    return feedstocks.where((feedstock) {
      return feedstock.name.toLowerCase().contains(lowercaseQuery) ||
             feedstock.description.toLowerCase().contains(lowercaseQuery) ||
             feedstock.type.toLowerCase().contains(lowercaseQuery) ||
             feedstock.category.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  /// Get all unique categories
  static Future<List<String>> getCategories() async {
    final feedstocks = await loadFeedstocks();
    final categories = feedstocks
        .map((feedstock) => feedstock.category)
        .toSet()
        .toList();
    categories.sort();
    return categories;
  }

  /// Get all unique types
  static Future<List<String>> getTypes() async {
    final feedstocks = await loadFeedstocks();
    final types = feedstocks
        .map((feedstock) => feedstock.type)
        .toSet()
        .toList();
    types.sort();
    return types;
  }

  /// Filter feedstocks by multiple criteria
  static Future<List<Feedstock>> filterFeedstocks({
    String? category,
    String? type,
    double? minCarbonContent,
    double? maxCarbonContent,
    double? minMoistureContent,
    double? maxMoistureContent,
    String? availability,
  }) async {
    final feedstocks = await loadFeedstocks();
    
    return feedstocks.where((feedstock) {
      // Category filter
      if (category != null && category.isNotEmpty) {
        if (feedstock.category.toLowerCase() != category.toLowerCase()) {
          return false;
        }
      }
      
      // Type filter
      if (type != null && type.isNotEmpty) {
        if (feedstock.type.toLowerCase() != type.toLowerCase()) {
          return false;
        }
      }
      
      // Carbon content filter
      if (minCarbonContent != null) {
        if (feedstock.carbonContent < minCarbonContent) {
          return false;
        }
      }
      if (maxCarbonContent != null) {
        if (feedstock.carbonContent > maxCarbonContent) {
          return false;
        }
      }
      
      // Moisture content filter
      if (minMoistureContent != null) {
        if (feedstock.moistureContent < minMoistureContent) {
          return false;
        }
      }
      if (maxMoistureContent != null) {
        if (feedstock.moistureContent > maxMoistureContent) {
          return false;
        }
      }
      
      // Availability filter
      if (availability != null && availability.isNotEmpty) {
        if (feedstock.availability.toLowerCase() != availability.toLowerCase()) {
          return false;
        }
      }
      
      return true;
    }).toList();
  }

  /// Get feedstock statistics
  static Future<Map<String, dynamic>> getFeedstockStatistics() async {
    final feedstocks = await loadFeedstocks();
    
    if (feedstocks.isEmpty) {
      return {
        'totalCount': 0,
        'categoryCount': 0,
        'typeCount': 0,
        'avgCarbonContent': 0.0,
        'avgMoistureContent': 0.0,
      };
    }
    
    final categories = await getCategories();
    final types = await getTypes();
    
    final avgCarbonContent = feedstocks
        .map((f) => f.carbonContent)
        .reduce((a, b) => a + b) / feedstocks.length;
    
    final avgMoistureContent = feedstocks
        .map((f) => f.moistureContent)
        .reduce((a, b) => a + b) / feedstocks.length;
    
    return {
      'totalCount': feedstocks.length,
      'categoryCount': categories.length,
      'typeCount': types.length,
      'avgCarbonContent': avgCarbonContent,
      'avgMoistureContent': avgMoistureContent,
    };
  }

  /// Clear cached data (useful for testing or data refresh)
  static void clearCache() {
    _cachedFeedstocks = null;
  }

  /// Refresh feedstock data
  static Future<List<Feedstock>> refreshFeedstocks() async {
    clearCache();
    return await loadFeedstocks();
  }
}