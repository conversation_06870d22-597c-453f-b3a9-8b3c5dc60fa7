import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/carbon_project.dart';

class CarbonProjectService {
  static const String _assetPath = 'assets/data/carbon_projects.json';
  static List<CarbonProject>? _cachedProjects;

  /// Load carbon projects from JSON asset
  static Future<List<CarbonProject>> loadProjects() async {
    if (_cachedProjects != null) {
      return _cachedProjects!;
    }

    try {
      final String jsonString = await rootBundle.loadString(_assetPath);
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      final List<dynamic> projectsJson = jsonData['projects'] as List;
      
      _cachedProjects = projectsJson
          .map((json) => CarbonProject.fromJson(json as Map<String, dynamic>))
          .toList();
      
      return _cachedProjects!;
    } catch (e) {
      print('Error loading carbon projects: $e');
      return [];
    }
  }

  /// Get project by ID
  static Future<CarbonProject?> getProjectById(String id) async {
    final projects = await loadProjects();
    try {
      return projects.firstWhere((project) => project.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Filter projects by status
  static Future<List<CarbonProject>> getProjectsByStatus(String status) async {
    final projects = await loadProjects();
    return projects.where((project) => project.status == status).toList();
  }

  /// Filter projects by type
  static Future<List<CarbonProject>> getProjectsByType(String type) async {
    final projects = await loadProjects();
    return projects.where((project) => project.projectType == type).toList();
  }

  /// Search projects by name or description
  static Future<List<CarbonProject>> searchProjects(String query) async {
    if (query.isEmpty) {
      return loadProjects();
    }

    final projects = await loadProjects();
    final lowercaseQuery = query.toLowerCase();
    
    return projects.where((project) {
      return project.name.toLowerCase().contains(lowercaseQuery) ||
             project.description.toLowerCase().contains(lowercaseQuery) ||
             project.location.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  /// Get unique project types
  static Future<List<String>> getProjectTypes() async {
    final projects = await loadProjects();
    final types = projects.map((project) => project.projectType).toSet().toList();
    types.sort();
    return types;
  }

  /// Get unique project statuses
  static Future<List<String>> getProjectStatuses() async {
    final projects = await loadProjects();
    final statuses = projects.map((project) => project.status).toSet().toList();
    statuses.sort();
    return statuses;
  }

  /// Get unique verification standards
  static Future<List<String>> getVerificationStandards() async {
    final projects = await loadProjects();
    final standards = projects.map((project) => project.verificationStandard).toSet().toList();
    standards.sort();
    return standards;
  }

  /// Filter projects by multiple criteria
  static Future<List<CarbonProject>> filterProjects({
    String? status,
    String? projectType,
    String? verificationStandard,
    DateTime? startDateAfter,
    DateTime? startDateBefore,
    double? minCarbonSequestered,
    double? maxCarbonSequestered,
  }) async {
    final projects = await loadProjects();
    
    return projects.where((project) {
      if (status != null && project.status != status) return false;
      if (projectType != null && project.projectType != projectType) return false;
      if (verificationStandard != null && project.verificationStandard != verificationStandard) return false;
      if (startDateAfter != null && project.startDate.isBefore(startDateAfter)) return false;
      if (startDateBefore != null && project.startDate.isAfter(startDateBefore)) return false;
      if (minCarbonSequestered != null && project.carbonSequestered < minCarbonSequestered) return false;
      if (maxCarbonSequestered != null && project.carbonSequestered > maxCarbonSequestered) return false;
      
      return true;
    }).toList();
  }

  /// Get project statistics
  static Future<Map<String, dynamic>> getProjectStatistics() async {
    final projects = await loadProjects();
    
    if (projects.isEmpty) {
      return {
        'totalProjects': 0,
        'activeProjects': 0,
        'completedProjects': 0,
        'totalCarbonSequestered': 0.0,
        'totalCreditsGenerated': 0.0,
        'totalCreditsVerified': 0.0,
        'totalCreditsSold': 0.0,
        'totalRevenue': 0.0,
        'averageCreditPrice': 0.0,
      };
    }
    
    final totalCarbonSequestered = projects.fold<double>(
      0.0, (sum, project) => sum + project.carbonSequestered);
    final totalCreditsGenerated = projects.fold<double>(
      0.0, (sum, project) => sum + project.creditsGenerated);
    final totalCreditsVerified = projects.fold<double>(
      0.0, (sum, project) => sum + project.creditsVerified);
    final totalCreditsSold = projects.fold<double>(
      0.0, (sum, project) => sum + project.creditsSold);
    final totalRevenue = projects.fold<double>(
      0.0, (sum, project) => sum + project.totalRevenue);
    
    final projectsWithPrice = projects.where((p) => p.creditPrice > 0).toList();
    final averageCreditPrice = projectsWithPrice.isNotEmpty
        ? projectsWithPrice.fold<double>(0.0, (sum, project) => sum + project.creditPrice) / projectsWithPrice.length
        : 0.0;
    
    return {
      'totalProjects': projects.length,
      'activeProjects': projects.where((p) => p.status == 'active').length,
      'completedProjects': projects.where((p) => p.status == 'completed').length,
      'planningProjects': projects.where((p) => p.status == 'planning').length,
      'verifiedProjects': projects.where((p) => p.status == 'verified').length,
      'totalCarbonSequestered': totalCarbonSequestered,
      'totalCreditsGenerated': totalCreditsGenerated,
      'totalCreditsVerified': totalCreditsVerified,
      'totalCreditsSold': totalCreditsSold,
      'totalRevenue': totalRevenue,
      'averageCreditPrice': averageCreditPrice,
      'verificationRate': totalCreditsGenerated > 0 ? totalCreditsVerified / totalCreditsGenerated : 0.0,
      'salesRate': totalCreditsVerified > 0 ? totalCreditsSold / totalCreditsVerified : 0.0,
    };
  }

  /// Calculate potential carbon credits from biochar production
  static double calculatePotentialCredits({
    required double biocharAmount, // in tons
    required double carbonContent, // percentage (0-100)
    double permanenceFactor = 0.8, // default 80% permanence
    double leakageFactor = 0.05, // default 5% leakage
  }) {
    // Carbon sequestered = biochar amount × carbon content × permanence factor × (1 - leakage factor)
    final carbonSequestered = biocharAmount * (carbonContent / 100) * permanenceFactor * (1 - leakageFactor);
    
    // Convert carbon to CO2 equivalent (molecular weight ratio: 44/12)
    final co2Equivalent = carbonSequestered * (44.0 / 12.0);
    
    return co2Equivalent;
  }

  /// Estimate project revenue
  static double estimateRevenue({
    required double creditsGenerated,
    required double creditPrice,
    double verificationRate = 0.85, // default 85% verification rate
    double salesRate = 0.90, // default 90% sales rate
  }) {
    final verifiedCredits = creditsGenerated * verificationRate;
    final soldCredits = verifiedCredits * salesRate;
    return soldCredits * creditPrice;
  }

  /// Clear cached data
  static void clearCache() {
    _cachedProjects = null;
  }

  /// Refresh data (clear cache and reload)
  static Future<List<CarbonProject>> refreshProjects() async {
    clearCache();
    return loadProjects();
  }
}