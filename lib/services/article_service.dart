import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/article.dart';

class ArticleService {
  static List<Article>? _cachedArticles;
  
  /// Load articles from JSON asset file
  static Future<List<Article>> loadArticles() async {
    if (_cachedArticles != null) {
      return _cachedArticles!;
    }
    
    try {
      final String jsonString = await rootBundle.loadString('assets/data/articles.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      
      final List<dynamic> articlesJson = jsonData['articles'] ?? [];
      _cachedArticles = articlesJson.map((json) => Article.fromJson(json)).toList();
      
      return _cachedArticles!;
    } catch (e) {
      print('Error loading articles: $e');
      return [];
    }
  }
  
  /// Get articles by category
  static Future<List<Article>> getArticlesByCategory(String category) async {
    final articles = await loadArticles();
    if (category.toLowerCase() == 'all') {
      return articles;
    }
    return articles.where((article) => 
      article.category.toLowerCase() == category.toLowerCase()
    ).toList();
  }
  
  /// Get all unique categories
  static Future<List<String>> getCategories() async {
    final articles = await loadArticles();
    final categories = articles.map((article) => article.category).toSet().toList();
    categories.sort();
    return ['All', ...categories];
  }
  
  /// Search articles by title or content
  static Future<List<Article>> searchArticles(String query) async {
    if (query.isEmpty) {
      return await loadArticles();
    }
    
    final articles = await loadArticles();
    final lowercaseQuery = query.toLowerCase();
    
    return articles.where((article) {
      final titleMatch = article.title.toLowerCase().contains(lowercaseQuery);
      final summaryMatch = article.summary.toLowerCase().contains(lowercaseQuery);
      final contentMatch = article.content.any((section) => 
        section.title.toLowerCase().contains(lowercaseQuery) ||
        section.content.toLowerCase().contains(lowercaseQuery)
      );
      
      return titleMatch || summaryMatch || contentMatch;
    }).toList();
  }
  
  /// Get article by ID
  static Future<Article?> getArticleById(String id) async {
    final articles = await loadArticles();
    try {
      return articles.firstWhere((article) => article.id == id);
    } catch (e) {
      return null;
    }
  }
  
  /// Clear cached articles (useful for testing or refreshing)
  static void clearCache() {
    _cachedArticles = null;
  }
}