import '../models/feedstock.dart';
import '../models/calculator_result.dart';

class CalculatorService {
  // Biochar yield coefficients by feedstock type (typical ranges)
  static const Map<String, double> _yieldCoefficients = {
    'hardwood': 0.25,      // 25% yield
    'softwood': 0.22,      // 22% yield
    'bamboo': 0.28,        // 28% yield
    'rice_hulls': 0.35,    // 35% yield
    'corn_stover': 0.30,   // 30% yield
    'wheat_straw': 0.32,   // 32% yield
    'coconut_shells': 0.40, // 40% yield
    'poultry_litter': 0.45, // 45% yield
    'dairy_manure': 0.35,  // 35% yield
    'other': 0.25,         // Default 25% yield
  };

  // Carbon content coefficients (percentage of biochar that is carbon)
  static const Map<String, double> _carbonContent = {
    'hardwood': 0.85,      // 85% carbon
    'softwood': 0.82,      // 82% carbon
    'bamboo': 0.88,        // 88% carbon
    'rice_hulls': 0.75,    // 75% carbon
    'corn_stover': 0.78,   // 78% carbon
    'wheat_straw': 0.76,   // 76% carbon
    'coconut_shells': 0.90, // 90% carbon
    'poultry_litter': 0.65, // 65% carbon
    'dairy_manure': 0.60,  // 60% carbon
    'other': 0.75,         // Default 75% carbon
  };

  // Energy content coefficients (MJ/kg of biochar)
  static const Map<String, double> _energyContent = {
    'hardwood': 28.5,
    'softwood': 26.8,
    'bamboo': 30.2,
    'rice_hulls': 22.4,
    'corn_stover': 24.6,
    'wheat_straw': 23.8,
    'coconut_shells': 32.1,
    'poultry_litter': 18.5,
    'dairy_manure': 16.2,
    'other': 25.0,
  };

  /// Calculate biochar production results
  static CalculatorResult calculateBiocharYield({
    required Feedstock feedstock,
    required double feedstockAmount, // in kg
    required double moistureContent, // percentage (0-100)
    required double pyrolysisTemperature, // in Celsius
    required double residenceTime, // in minutes
  }) {
    // Adjust for moisture content
    final dryFeedstockAmount = feedstockAmount * (1 - moistureContent / 100);
    
    // Get base yield coefficient
    final baseYield = _getYieldCoefficient(feedstock.type);
    
    // Adjust yield based on pyrolysis conditions
    final temperatureAdjustment = _calculateTemperatureAdjustment(pyrolysisTemperature);
    final timeAdjustment = _calculateTimeAdjustment(residenceTime);
    
    final adjustedYield = baseYield * temperatureAdjustment * timeAdjustment;
    
    // Calculate biochar production
    final biocharYield = dryFeedstockAmount * adjustedYield;
    
    // Calculate carbon sequestration
    final carbonContent = _getCarbonContent(feedstock.type);
    final carbonSequestered = biocharYield * carbonContent;
    
    // Calculate CO2 equivalent (carbon to CO2 conversion factor: 3.67)
    final co2Equivalent = carbonSequestered * 3.67;
    
    // Calculate energy content
    final energyContent = _getEnergyContent(feedstock.type);
    final totalEnergy = biocharYield * energyContent;
    
    // Calculate efficiency metrics
    final massEfficiency = (biocharYield / feedstockAmount) * 100;
    final carbonEfficiency = (carbonSequestered / (dryFeedstockAmount * 0.5)) * 100; // Assume 50% carbon in dry feedstock
    
    return CalculatorResult(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      feedstockId: feedstock.id,
      feedstockAmount: feedstockAmount,
      moistureContent: moistureContent,
      biocharYield: biocharYield,
      carbonContent: feedstock.carbonContent,
      carbonSequestered: carbonSequestered,
      energyContent: totalEnergy,
      carbonEfficiency: carbonEfficiency,
      pyrolysisTemperature: pyrolysisTemperature,
      residenceTime: residenceTime,
      feedstockType: feedstock.type,
      economicValue: {'totalValue': 0.0}, // Default value
      calculatedAt: DateTime.now(),
      parameters: {
        'feedstockAmount': feedstockAmount,
        'moistureContent': moistureContent,
        'pyrolysisTemperature': pyrolysisTemperature,
        'residenceTime': residenceTime,
      },
    );
  }

  /// Get yield coefficient for feedstock type
  static double _getYieldCoefficient(String feedstockType) {
    final normalizedType = feedstockType.toLowerCase().replaceAll(' ', '_');
    return _yieldCoefficients[normalizedType] ?? _yieldCoefficients['other']!;
  }

  /// Get carbon content for feedstock type
  static double _getCarbonContent(String feedstockType) {
    final normalizedType = feedstockType.toLowerCase().replaceAll(' ', '_');
    return _carbonContent[normalizedType] ?? _carbonContent['other']!;
  }

  /// Get energy content for feedstock type
  static double _getEnergyContent(String feedstockType) {
    final normalizedType = feedstockType.toLowerCase().replaceAll(' ', '_');
    return _energyContent[normalizedType] ?? _energyContent['other']!;
  }

  /// Calculate temperature adjustment factor
  static double _calculateTemperatureAdjustment(double temperature) {
    // Optimal temperature range: 400-500°C
    if (temperature >= 400 && temperature <= 500) {
      return 1.0; // No adjustment
    } else if (temperature < 400) {
      // Lower temperatures reduce yield
      return 0.7 + (temperature - 300) / 400 * 0.3;
    } else {
      // Higher temperatures also reduce yield due to gasification
      return 1.0 - (temperature - 500) / 300 * 0.2;
    }
  }

  /// Calculate residence time adjustment factor
  static double _calculateTimeAdjustment(double timeMinutes) {
    // Optimal residence time: 30-60 minutes
    if (timeMinutes >= 30 && timeMinutes <= 60) {
      return 1.0; // No adjustment
    } else if (timeMinutes < 30) {
      // Shorter times reduce conversion
      return 0.6 + timeMinutes / 30 * 0.4;
    } else {
      // Longer times have diminishing returns
      return 1.0 - (timeMinutes - 60) / 120 * 0.1;
    }
  }

  /// Calculate economic value (placeholder - would need market prices)
  static Map<String, double> calculateEconomicValue(CalculatorResult result) {
    // Placeholder values - would be updated with real market data
    const biocharPrice = 500.0; // USD per ton
    const carbonCreditPrice = 25.0; // USD per ton CO2
    
    final biocharValue = (result.biocharYield / 1000) * biocharPrice;
    final carbonCreditValue = (result.co2Equivalent / 1000) * carbonCreditPrice;
    
    return {
      'biocharValue': biocharValue,
      'carbonCreditValue': carbonCreditValue,
      'totalValue': biocharValue + carbonCreditValue,
    };
  }

  /// Validate input parameters
  static Map<String, String> validateInputs({
    required double feedstockAmount,
    required double moistureContent,
    required double pyrolysisTemperature,
    required double residenceTime,
  }) {
    final errors = <String, String>{};
    
    if (feedstockAmount <= 0) {
      errors['feedstockAmount'] = 'Feedstock amount must be greater than 0';
    }
    
    if (moistureContent < 0 || moistureContent > 95) {
      errors['moistureContent'] = 'Moisture content must be between 0% and 95%';
    }
    
    if (pyrolysisTemperature < 250 || pyrolysisTemperature > 800) {
      errors['pyrolysisTemperature'] = 'Temperature must be between 250°C and 800°C';
    }
    
    if (residenceTime < 5 || residenceTime > 300) {
      errors['residenceTime'] = 'Residence time must be between 5 and 300 minutes';
    }
    
    return errors;
  }
}