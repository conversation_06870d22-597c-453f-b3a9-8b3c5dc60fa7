import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import '../models/user_preferences.dart';
import '../models/calculator.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'biochar_app.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create user_preferences table
    await db.execute('''
      CREATE TABLE user_preferences (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        theme_mode TEXT NOT NULL DEFAULT 'system',
        preferred_units TEXT NOT NULL DEFAULT 'metric',
        default_feedstock TEXT,
        show_tutorials INTEGER NOT NULL DEFAULT 1,
        enable_notifications INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create calculator_history table
    await db.execute('''
      CREATE TABLE calculator_history (
        id TEXT PRIMARY KEY,
        feedstock_id TEXT NOT NULL,
        weight_kg REAL NOT NULL,
        temp_range TEXT NOT NULL,
        yield_kg REAL NOT NULL,
        carbon_storage REAL NOT NULL,
        created_at TEXT NOT NULL
      )
    ''');

    // Insert default user preferences
    await db.insert('user_preferences', {
      'theme_mode': 'system',
      'preferred_units': 'metric',
      'show_tutorials': 1,
      'enable_notifications': 1,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });
  }

  // User Preferences Methods
  Future<UserPreferences> getUserPreferences() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'user_preferences',
      limit: 1,
      orderBy: 'id DESC',
    );

    if (maps.isNotEmpty) {
      return UserPreferences.fromMap(maps.first);
    } else {
      // Return default preferences if none exist
      return UserPreferences(
        themeMode: 'system',
        preferredUnits: 'metric',
        showTutorials: true,
        enableNotifications: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }
  }

  Future<void> updateUserPreferences(UserPreferences preferences) async {
    final db = await database;
    final updatedPreferences = preferences.copyWith(
      updatedAt: DateTime.now(),
    );
    
    await db.update(
      'user_preferences',
      updatedPreferences.toMap(),
      where: 'id = ?',
      whereArgs: [preferences.id],
    );
  }

  // Calculator History Methods
  Future<void> saveCalculatorHistory(CalculatorHistory history) async {
    final db = await database;
    await db.insert(
      'calculator_history',
      history.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<CalculatorHistory>> getCalculatorHistory({
    int? limit,
    String? feedstockFilter,
  }) async {
    final db = await database;
    
    String whereClause = '';
    List<dynamic> whereArgs = [];
    
    if (feedstockFilter != null && feedstockFilter.isNotEmpty) {
      whereClause = 'WHERE feedstock_id = ?';
      whereArgs.add(feedstockFilter);
    }
    
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT * FROM calculator_history 
      $whereClause
      ORDER BY created_at DESC
      ${limit != null ? 'LIMIT $limit' : ''}
    ''', whereArgs);

    return List.generate(maps.length, (i) {
      return CalculatorHistory.fromMap(maps[i]);
    });
  }

  Future<void> deleteCalculatorHistory(String id) async {
    final db = await database;
    await db.delete(
      'calculator_history',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> clearAllCalculatorHistory() async {
    final db = await database;
    await db.delete('calculator_history');
  }

  Future<int> getCalculatorHistoryCount() async {
    final db = await database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM calculator_history');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  // Database utility methods
  Future<void> closeDatabase() async {
    final db = await database;
    await db.close();
  }

  Future<void> deleteDatabase() async {
    String path = join(await getDatabasesPath(), 'biochar_app.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}