# AdMob Integration Setup Guide

This document provides instructions for setting up and configuring AdMob banner ads in the Biochar app.

## Current Implementation

The app is now configured with AdMob banner ads that appear at the bottom of all main screens.

### Features Implemented

- ✅ Banner ads at the bottom of all main screens
- ✅ Test ad units for development
- ✅ Cross-platform support (Android & iOS)
- ✅ Responsive ad loading with fallback UI
- ✅ Clean integration with app theme

## Configuration Files Modified

### 1. Dependencies (`pubspec.yaml`)
```yaml
dependencies:
  google_mobile_ads: ^5.1.0
```

### 2. Android Configuration
- **File**: `android/app/src/main/AndroidManifest.xml`
- **Changes**: Added Internet permission and test App ID
```xml
<uses-permission android:name="android.permission.INTERNET" />
<meta-data
    android:name="com.google.android.gms.ads.APPLICATION_ID"
    android:value="ca-app-pub-****************~**********"/>
```

### 3. iOS Configuration
- **File**: `ios/Runner/Info.plist`
- **Changes**: Added test App ID
```xml
<key>GADApplicationIdentifier</key>
<string>ca-app-pub-****************~**********</string>
```

### 4. Code Implementation
- **Main initialization**: `lib/main.dart`
- **Banner widget**: `lib/widgets/admob_banner.dart`
- **Integration**: `lib/screens/main_screen.dart`

## Production Setup

### ⚠️ Important: Replace Test IDs Before Production

**Current test IDs (REPLACE before publishing):**

**Android:**
- App ID: `ca-app-pub-****************~**********`
- Banner Unit ID: `ca-app-pub-****************/**********`

**iOS:**
- App ID: `ca-app-pub-****************~**********`
- Banner Unit ID: `ca-app-pub-****************/**********`

### Steps for Production:

1. **Create AdMob Account**: Sign up at https://admob.google.com
2. **Create App**: Add your app to AdMob console
3. **Create Ad Units**: Create banner ad units for your app
4. **Replace IDs**: Update the following files with your actual IDs:

   **Android** (`android/app/src/main/AndroidManifest.xml`):
   ```xml
   <meta-data
       android:name="com.google.android.gms.ads.APPLICATION_ID"
       android:value="YOUR_ANDROID_APP_ID"/>
   ```

   **iOS** (`ios/Runner/Info.plist`):
   ```xml
   <key>GADApplicationIdentifier</key>
   <string>YOUR_IOS_APP_ID</string>
   ```

   **Banner Widget** (`lib/widgets/admob_banner.dart`):
   ```dart
   static const String _androidBannerAdUnitId = 'YOUR_ANDROID_BANNER_ID';
   static const String _iosBannerAdUnitId = 'YOUR_IOS_BANNER_ID';
   ```

## Ad Placement Strategy

### Current Placement
- **Location**: Bottom of all main screens (above bottom navigation)
- **Type**: Standard banner (320x50)
- **Behavior**: Persistent across all tabs

### Revenue Optimization Tips
1. **Banner Placement**: Bottom placement is non-intrusive but visible
2. **User Experience**: Ads don't interfere with navigation or content
3. **Loading States**: Graceful fallback when ads fail to load
4. **Theme Integration**: Ads blend with app's earth-tone theme

## Testing

### Test Ad Configuration
- The app is currently configured with Google's test ad units
- Test ads will show during development and testing
- No real revenue is generated from test ads

### Verification Steps
1. **Build App**: `flutter build apk --debug`
2. **Install on Device**: Install on physical device (ads don't show in simulator)
3. **Navigate App**: Switch between tabs to verify ad loading
4. **Check Logs**: Monitor console for any AdMob-related errors

## Troubleshooting

### Common Issues

**1. Ads Not Showing**
- Ensure device has internet connection
- Check that test IDs are correctly configured
- Verify AdMob SDK initialization in `main.dart`

**2. Build Errors**
- Run `flutter clean && flutter pub get`
- Check that all configuration files are properly saved

**3. iOS Build Issues**
- Ensure Info.plist is properly formatted XML
- Check that GADApplicationIdentifier is correctly spelled

### Development vs Production
- **Development**: Use test IDs (current configuration)
- **Production**: Replace with actual AdMob IDs before publishing

## Revenue Considerations

### Expected Performance
- **eCPM**: Varies by region ($0.50-$3.00 typical for banner ads)
- **Fill Rate**: Google AdMob typically has >95% fill rate
- **User Impact**: Bottom banners have minimal UX impact

### Optimization Opportunities
- **Smart Banners**: Consider implementing adaptive banners
- **Interstitial Ads**: Could add between major actions
- **Rewarded Ads**: Potential for premium features unlock

## Compliance

### Privacy Requirements
- **GDPR**: Will need consent management for EU users
- **CCPA**: California privacy compliance
- **COPPA**: Age-appropriate content settings

### App Store Guidelines
- **Google Play**: Ensure compliance with ad policy
- **App Store**: Follow Apple's advertising guidelines

## Monitoring

### Key Metrics to Track
- **Impressions**: Number of ad views
- **Click Rate**: User engagement with ads
- **Revenue**: Monthly earnings
- **User Retention**: Impact on app usage

### Recommended Tools
- **AdMob Console**: Primary revenue and performance tracking
- **Firebase Analytics**: User behavior and retention metrics
- **Google Analytics**: Detailed user journey analysis

## Support

For technical issues with AdMob integration:
1. Check [Google AdMob documentation](https://developers.google.com/admob)
2. Review [Flutter google_mobile_ads plugin documentation](https://pub.dev/packages/google_mobile_ads)
3. Monitor AdMob console for policy violations or account issues