# Task Completion Checklist

## Before Committing Code Changes

### 1. Code Quality Checks
```bash
# Run from biochar_app/ directory
flutter analyze
dart format --fix .
```

### 2. Testing
```bash
# Run all tests
flutter test

# Ensure tests pass and coverage is maintained
flutter test --coverage
```

### 3. Build Verification
```bash
# Ensure the app builds successfully
flutter build apk --debug
```

### 4. Dependencies Check
```bash
# Verify dependencies are up to date and secure
flutter pub deps
flutter pub outdated
```

### 5. Asset Validation
- Verify JSON files in `assets/data/` are valid
- Check that image assets are properly referenced
- Ensure asset declarations in `pubspec.yaml` are correct

## Development Workflow

### Adding New Features
1. Create models in `lib/models/` if needed
2. Implement services in `lib/services/`
3. Create screens in `lib/screens/`
4. Add widgets in `lib/widgets/` for reusable components
5. Update navigation in `MainScreen` if adding new tabs
6. Add corresponding JSON data in `assets/data/` if needed
7. Write tests in `test/`

### Modifying Existing Features
1. Check for references using `find_referencing_symbols` tool
2. Update all dependent code
3. Verify backward compatibility
4. Update tests accordingly

### Performance Considerations
- Keep JSON files under target sizes (articles <500KB, feedstocks <200KB)
- Optimize images (use compressed PNG)
- Test on low-end devices (1GB RAM minimum)