# Biochar App Project Overview

## Purpose
The Biochar App is a mobile application designed to educate users about biochar, provide tools for estimating biochar yield, and offer reference data on biomass feedstocks. It targets farmers, researchers, and sustainability enthusiasts, particularly those in low-connectivity or rural areas where internet access is unreliable.

## Key Features
1. **Biochar Knowledge Hub** - Preloaded articles on biochar fundamentals stored locally in JSON format
2. **Biochar Calculator** - Estimates biochar yield and carbon storage based on feedstock type, weight, and pyrolysis temperature
3. **Feedstock Database** - Searchable database of biomass materials for biochar production
4. **Carbon Credit Tracking** - Tools for estimating and tracking carbon credits

## Tech Stack
- **Frontend**: Flutter (Dart) for cross-platform mobile app (Android and iOS)
- **Data Storage**: Static JSON files for articles and feedstock data; SQLite for user preferences and calculator history
- **State Management**: Provider package
- **Local Database**: sqflite for SQLite operations
- **Offline-first**: All functionality works without internet connectivity

## Target Platforms
- Android 5.0+
- iOS 12.0+
- Minimum requirements: 1GB RAM, 100MB storage