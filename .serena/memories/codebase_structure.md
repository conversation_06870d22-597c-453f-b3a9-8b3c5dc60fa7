# Codebase Structure

## Project Layout
```
biochar.solo/
├── prd_r2.txt                    # Product Requirements Document
└── biochar_app/                  # Flutter application
    ├── lib/
    │   ├── main.dart             # App entry point
    │   ├── models/               # Data models (Article, Feedstock, Calculator, etc.)
    │   ├── screens/              # UI screens for each feature
    │   ├── services/             # Business logic and data services
    │   ├── theme/                # App theming with earth tones
    │   └── widgets/              # Reusable UI components
    ├── assets/
    │   ├── data/                 # JSON files for articles, feedstocks, coefficients
    │   └── images/               # Feedstock thumbnails and other images
    ├── test/                     # Unit and widget tests
    └── pubspec.yaml              # Dependencies and Flutter configuration
```

## Key Architecture Components
- **Models**: Data classes for Article, Feedstock, Calculator, CarbonProject, UserPreferences
- **Services**: DatabaseHelper (SQLite), ArticleService, FeedstockService, CalculatorService, CarbonProjectService
- **Screens**: MainScreen with bottom navigation, individual screens for each feature
- **Theme**: AppTheme with earth-tone color scheme (browns, greens, cream)
- **State Management**: Provider pattern for state management

## Data Storage
- **JSON Assets**: Articles, feedstocks, calculation coefficients stored in `assets/data/`
- **SQLite Database**: User preferences, calculator history via DatabaseHelper service
- **Images**: Feedstock thumbnails in `assets/images/`