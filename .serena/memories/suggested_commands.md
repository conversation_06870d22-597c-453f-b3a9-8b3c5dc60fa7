# Flutter Development Commands

## Essential Flutter Commands (run from biochar_app/ directory)

### Getting Dependencies
```bash
flutter pub get
```

### Running the App
```bash
# Debug mode (hot reload enabled)
flutter run

# Run on specific device
flutter run -d <device_id>

# List available devices
flutter devices
```

### Code Quality and Analysis
```bash
# Run static analysis
flutter analyze

# Run linter
dart fix --dry-run
dart fix --apply

# Format code
dart format .
dart format --fix .
```

### Testing
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/widget_test.dart

# Run tests with coverage
flutter test --coverage
```

### Building
```bash
# Build APK for Android
flutter build apk

# Build App Bundle for Android
flutter build appbundle

# Build for iOS (requires macOS)
flutter build ios

# Build for release
flutter build apk --release
```

### Cleaning and Maintenance
```bash
# Clean build files
flutter clean

# Upgrade dependencies
flutter pub upgrade

# Check for outdated packages
flutter pub outdated
```

## Windows-Specific Commands
Since this is a Windows environment, use these commands in Command Prompt or PowerShell:
- Use `dir` instead of `ls`
- Use `type` instead of `cat`
- Paths use backslashes `\`