# Code Style and Conventions

## Dart/Flutter Style Guide
The project follows standard Dart conventions with Flutter lints enabled.

### Linting Configuration
- Uses `flutter_lints: ^5.0.0` package
- Configuration in `analysis_options.yaml` includes `package:flutter_lints/flutter.yaml`
- Standard Flutter recommended lints are active

### Naming Conventions
- **Classes**: PascalCase (e.g., `BiocharApp`, `DatabaseHelper`)
- **Methods/Functions**: camelCase (e.g., `getUserPreferences`, `saveCalculatorHistory`)
- **Variables**: camelCase (e.g., `primaryBrown`, `lightTheme`)
- **Constants**: camelCase with descriptive names (e.g., `forestGreen`, `warmWhite`)
- **Private members**: Start with underscore (e.g., `_instance`, `_database`)

### File Organization
- **Models**: Data classes in `lib/models/`
- **Services**: Business logic in `lib/services/`
- **Screens**: UI screens in `lib/screens/`
- **Widgets**: Reusable components in `lib/widgets/`
- **Theme**: Styling in `lib/theme/`

### Code Patterns
- **Singleton Pattern**: Used in `DatabaseHelper` class
- **Provider Pattern**: Used for state management
- **Future/Async**: Used for database operations and file loading
- **Null Safety**: Enabled (Dart 3.8.1+)

### Dependencies Management
Key dependencies used:
- `provider: ^6.1.1` - State management
- `sqflite: ^2.3.0` - Local database
- `path_provider: ^2.1.1` - File system access
- `json_annotation: ^4.8.1` - JSON serialization
- `uuid: ^4.2.1` - UUID generation