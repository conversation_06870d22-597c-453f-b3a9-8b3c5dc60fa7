#!/bin/bash

# Flutter Test Coverage Script

echo "🧪 Running comprehensive test suite with coverage..."

# Install dependencies
echo "📦 Installing dependencies..."
flutter pub get

# Run static analysis
echo "🔍 Running static analysis..."
flutter analyze

# Format code
echo "🎨 Formatting code..."
dart format --fix .

# Clean previous coverage
echo "🧹 Cleaning previous coverage..."
rm -rf coverage

# Run unit and widget tests with coverage
echo "🔬 Running unit and widget tests with coverage..."
flutter test --coverage

# Generate HTML coverage report
echo "📊 Generating HTML coverage report..."
genhtml coverage/lcov.info -o coverage/html

# Run integration tests
echo "🏗️ Running integration tests..."
flutter test integration_test/

echo "✅ Test suite completed!"
echo "📈 Coverage report available at: coverage/html/index.html"

# Display coverage summary
if [ -f "coverage/lcov.info" ]; then
    echo "📊 Coverage Summary:"
    lcov --summary coverage/lcov.info
fi