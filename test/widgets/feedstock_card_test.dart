import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:biochar_app/widgets/feedstock_card.dart';
import 'package:biochar_app/models/feedstock.dart';

void main() {
  group('FeedstockCard', () {
    late Feedstock testFeedstock;

    setUp(() {
      testFeedstock = Feedstock(
        id: 'test_id',
        name: 'Test Feedstock',
        description: 'This is a test feedstock for unit testing purposes.',
        type: 'Hardwood',
        category: 'Forest Residues',
        thumbnail: 'test.png',
        yieldRange: YieldRange(min: 20.0, max: 30.0),
        pyrolysisTemp: '450',
        considerations: 'Test considerations',
        advantages: ['Advantage 1', 'Advantage 2'],
        disadvantages: ['Disadvantage 1'],
        sustainabilityScore: 8.5,
        cost: 100.0,
        costUnit: 'per ton',
        processingRequirements: ['Drying', 'Grinding'],
        bulkDensity: 600.0,
        energyContent: 18.5,
        biocharYield: 25.0,
        pyrolysisTemperature: 450.0,
        moistureContent: 10.0,
        ashContent: 5.0,
        volatileMatter: 75.0,
        fixedCarbon: 20.0,
        availability: 'High',
        seasonality: 'Year-round',
        carbonContent: 45.0,
      );
    });

    Widget createTestWidget(Widget child) {
      return MaterialApp(
        home: Scaffold(
          body: child,
        ),
      );
    }

    testWidgets('should display feedstock information correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        FeedstockCard(feedstock: testFeedstock),
      ));

      expect(find.text('Test Feedstock'), findsOneWidget);
      expect(find.text('Hardwood'), findsOneWidget);
      expect(find.text('This is a test feedstock for unit testing purposes.'), findsOneWidget);
      expect(find.text('Forest Residues'), findsOneWidget);
      expect(find.text('High'), findsOneWidget);
      expect(find.text('\$100/per ton'), findsOneWidget);
    });

    testWidgets('should display property chips correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        FeedstockCard(feedstock: testFeedstock),
      ));

      expect(find.text('Carbon'), findsOneWidget);
      expect(find.text('45.0%'), findsOneWidget);
      expect(find.text('Moisture'), findsOneWidget);
      expect(find.text('10.0%'), findsOneWidget);
      expect(find.text('Yield'), findsOneWidget);
      expect(find.text('25%'), findsOneWidget);
    });

    testWidgets('should show correct category icon for forest residues', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        FeedstockCard(feedstock: testFeedstock),
      ));

      expect(find.byIcon(Icons.forest), findsOneWidget);
    });

    testWidgets('should handle different availability levels', (WidgetTester tester) async {
      final mediumAvailabilityFeedstock = Feedstock(
        id: 'test_id',
        name: 'Test Feedstock',
        description: 'Test description',
        type: 'Hardwood',
        category: 'Forest Residues',
        thumbnail: '',
        yieldRange: YieldRange(min: 20.0, max: 30.0),
        pyrolysisTemp: '450',
        considerations: '',
        advantages: [],
        disadvantages: [],
        sustainabilityScore: 8.0,
        cost: 0.0,
        costUnit: 'per ton',
        processingRequirements: [],
        bulkDensity: 600.0,
        energyContent: 18.0,
        biocharYield: 25.0,
        pyrolysisTemperature: 450.0,
        moistureContent: 10.0,
        ashContent: 5.0,
        volatileMatter: 75.0,
        fixedCarbon: 20.0,
        availability: 'Medium',
        seasonality: '',
        carbonContent: 45.0,
      );

      await tester.pumpWidget(createTestWidget(
        FeedstockCard(feedstock: mediumAvailabilityFeedstock),
      ));

      expect(find.text('Medium'), findsOneWidget);
    });

    testWidgets('should call onTap when tapped', (WidgetTester tester) async {
      bool tapped = false;
      void onTap() {
        tapped = true;
      }

      await tester.pumpWidget(createTestWidget(
        FeedstockCard(
          feedstock: testFeedstock,
          onTap: onTap,
        ),
      ));

      await tester.tap(find.byType(FeedstockCard));
      expect(tapped, true);
    });

    testWidgets('should not show cost when cost is zero', (WidgetTester tester) async {
      final noCostFeedstock = Feedstock(
        id: 'test_id',
        name: 'Test Feedstock',
        description: 'Test description',
        type: 'Hardwood',
        category: 'Forest Residues',
        thumbnail: '',
        yieldRange: YieldRange(min: 20.0, max: 30.0),
        pyrolysisTemp: '450',
        considerations: '',
        advantages: [],
        disadvantages: [],
        sustainabilityScore: 8.0,
        cost: 0.0,
        costUnit: 'per ton',
        processingRequirements: [],
        bulkDensity: 600.0,
        energyContent: 18.0,
        biocharYield: 25.0,
        pyrolysisTemperature: 450.0,
        moistureContent: 10.0,
        ashContent: 5.0,
        volatileMatter: 75.0,
        fixedCarbon: 20.0,
        availability: 'High',
        seasonality: '',
        carbonContent: 45.0,
      );

      await tester.pumpWidget(createTestWidget(
        FeedstockCard(feedstock: noCostFeedstock),
      ));

      expect(find.text('\$0/per ton'), findsNothing);
    });

    testWidgets('should handle different categories with correct icons', (WidgetTester tester) async {
      final agricultureFeedstock = Feedstock(
        id: 'test_id',
        name: 'Test Feedstock',
        description: 'Test description',
        type: 'Crop',
        category: 'Crop Residues',
        thumbnail: '',
        yieldRange: YieldRange(min: 20.0, max: 30.0),
        pyrolysisTemp: '450',
        considerations: '',
        advantages: [],
        disadvantages: [],
        sustainabilityScore: 8.0,
        cost: 0.0,
        costUnit: 'per ton',
        processingRequirements: [],
        bulkDensity: 600.0,
        energyContent: 18.0,
        biocharYield: 25.0,
        pyrolysisTemperature: 450.0,
        moistureContent: 10.0,
        ashContent: 5.0,
        volatileMatter: 75.0,
        fixedCarbon: 20.0,
        availability: 'High',
        seasonality: '',
        carbonContent: 45.0,
      );

      await tester.pumpWidget(createTestWidget(
        FeedstockCard(feedstock: agricultureFeedstock),
      ));

      expect(find.byIcon(Icons.agriculture), findsOneWidget);
    });

    testWidgets('should truncate long descriptions', (WidgetTester tester) async {
      final longDescriptionFeedstock = Feedstock(
        id: 'test_id',
        name: 'Test Feedstock',
        description: 'This is a very long description that should be truncated when displayed in the feedstock card widget to ensure proper layout and readability.',
        type: 'Hardwood',
        category: 'Forest Residues',
        thumbnail: '',
        yieldRange: YieldRange(min: 20.0, max: 30.0),
        pyrolysisTemp: '450',
        considerations: '',
        advantages: [],
        disadvantages: [],
        sustainabilityScore: 8.0,
        cost: 0.0,
        costUnit: 'per ton',
        processingRequirements: [],
        bulkDensity: 600.0,
        energyContent: 18.0,
        biocharYield: 25.0,
        pyrolysisTemperature: 450.0,
        moistureContent: 10.0,
        ashContent: 5.0,
        volatileMatter: 75.0,
        fixedCarbon: 20.0,
        availability: 'High',
        seasonality: '',
        carbonContent: 45.0,
      );

      await tester.pumpWidget(createTestWidget(
        FeedstockCard(feedstock: longDescriptionFeedstock),
      ));

      final textWidget = tester.widget<Text>(
        find.text(longDescriptionFeedstock.description),
      );
      
      expect(textWidget.maxLines, 2);
      expect(textWidget.overflow, TextOverflow.ellipsis);
    });
  });
}