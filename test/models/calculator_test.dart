import 'package:flutter_test/flutter_test.dart';
import 'package:biochar_app/models/calculator.dart';

void main() {
  group('CalculatorInput', () {
    test('should create CalculatorInput from JSON', () {
      final json = {
        'feedstock_id': 'wood_chips',
        'weight_kg': 100.0,
        'temp_range': '400-500',
      };

      final input = CalculatorInput.fromJson(json);

      expect(input.feedstockId, 'wood_chips');
      expect(input.weightKg, 100.0);
      expect(input.tempRange, '400-500');
    });

    test('should convert CalculatorInput to JSON', () {
      final input = CalculatorInput(
        feedstockId: 'wood_chips',
        weightKg: 100.0,
        tempRange: '400-500',
      );

      final json = input.toJson();

      expect(json['feedstock_id'], 'wood_chips');
      expect(json['weight_kg'], 100.0);
      expect(json['temp_range'], '400-500');
    });
  });

  group('CalculatorOutput', () {
    test('should create CalculatorOutput from JSON', () {
      final json = {
        'yield_kg': 25.0,
        'carbon_storage_kg_co2_eq': 75.0,
      };

      final output = CalculatorOutput.fromJson(json);

      expect(output.yieldKg, 25.0);
      expect(output.carbonStorageKgCo2Eq, 75.0);
    });

    test('should convert CalculatorOutput to JSON', () {
      final output = CalculatorOutput(
        yieldKg: 25.0,
        carbonStorageKgCo2Eq: 75.0,
      );

      final json = output.toJson();

      expect(json['yield_kg'], 25.0);
      expect(json['carbon_storage_kg_co2_eq'], 75.0);
    });
  });

  group('CalculatorHistory', () {
    final testDateTime = DateTime(2024, 1, 15, 10, 30);

    test('should create CalculatorHistory from JSON', () {
      final json = {
        'id': 'calc_123',
        'feedstock_id': 'wood_chips',
        'weight_kg': 100.0,
        'temp_range': '400-500',
        'yield_kg': 25.0,
        'carbon_storage': 75.0,
        'created_at': testDateTime.toIso8601String(),
      };

      final history = CalculatorHistory.fromJson(json);

      expect(history.id, 'calc_123');
      expect(history.feedstockId, 'wood_chips');
      expect(history.weightKg, 100.0);
      expect(history.tempRange, '400-500');
      expect(history.yieldKg, 25.0);
      expect(history.carbonStorage, 75.0);
      expect(history.createdAt, testDateTime);
    });

    test('should create CalculatorHistory from Map (database)', () {
      final map = {
        'id': 'calc_123',
        'feedstock_id': 'wood_chips',
        'weight_kg': 100.0,
        'temp_range': '400-500',
        'yield_kg': 25.0,
        'carbon_storage': 75.0,
        'created_at': testDateTime.toIso8601String(),
      };

      final history = CalculatorHistory.fromMap(map);

      expect(history.id, 'calc_123');
      expect(history.feedstockId, 'wood_chips');
      expect(history.createdAt, testDateTime);
    });

    test('should convert CalculatorHistory to JSON', () {
      final history = CalculatorHistory(
        id: 'calc_123',
        feedstockId: 'wood_chips',
        weightKg: 100.0,
        tempRange: '400-500',
        yieldKg: 25.0,
        carbonStorage: 75.0,
        createdAt: testDateTime,
      );

      final json = history.toJson();

      expect(json['id'], 'calc_123');
      expect(json['feedstock_id'], 'wood_chips');
      expect(json['weight_kg'], 100.0);
      expect(json['created_at'], testDateTime.toIso8601String());
    });

    test('should convert CalculatorHistory to Map (database)', () {
      final history = CalculatorHistory(
        id: 'calc_123',
        feedstockId: 'wood_chips',
        weightKg: 100.0,
        tempRange: '400-500',
        yieldKg: 25.0,
        carbonStorage: 75.0,
        createdAt: testDateTime,
      );

      final map = history.toMap();

      expect(map['id'], 'calc_123');
      expect(map['feedstock_id'], 'wood_chips');
      expect(map['weight_kg'], 100.0);
      expect(map['created_at'], testDateTime.toIso8601String());
    });
  });
}