import 'package:flutter_test/flutter_test.dart';
import 'package:biochar_app/models/calculator_result.dart';

void main() {
  group('CalculatorResult', () {
    late CalculatorResult testResult;
    late DateTime testDate;

    setUp(() {
      testDate = DateTime(2024, 1, 15, 10, 30);
      testResult = CalculatorResult(
        id: 'result_123',
        feedstockId: 'hardwood_chips',
        feedstockAmount: 100.0,
        biocharYield: 25.0,
        carbonContent: 45.0,
        carbonSequestered: 21.25,
        energyContent: 712.5,
        carbonEfficiency: 85.0,
        feedstockType: 'hardwood',
        pyrolysisTemperature: 450.0,
        residenceTime: 45.0,
        moistureContent: 10.0,
        economicValue: {
          'biocharValue': 12.5,
          'carbonCreditValue': 5.31,
          'totalValue': 17.81
        },
        calculatedAt: testDate,
        parameters: {
          'feedstockAmount': 100.0,
          'moistureContent': 10.0,
          'pyrolysisTemperature': 450.0,
          'residenceTime': 45.0,
        },
      );
    });

    test('should create CalculatorResult from JSON', () {
      final json = {
        'id': 'result_123',
        'feedstock_id': 'hardwood_chips',
        'feedstock_amount': 100.0,
        'biochar_yield': 25.0,
        'carbon_content': 45.0,
        'carbon_sequestered': 21.25,
        'energy_content': 712.5,
        'carbon_efficiency': 85.0,
        'feedstock_type': 'hardwood',
        'pyrolysis_temperature': 450.0,
        'residence_time': 45.0,
        'moisture_content': 10.0,
        'economic_value': {
          'biocharValue': 12.5,
          'carbonCreditValue': 5.31,
          'totalValue': 17.81
        },
        'calculated_at': testDate.toIso8601String(),
        'parameters': {
          'feedstockAmount': 100.0,
          'moistureContent': 10.0,
          'pyrolysisTemperature': 450.0,
          'residenceTime': 45.0,
        },
      };

      final result = CalculatorResult.fromJson(json);

      expect(result.id, 'result_123');
      expect(result.feedstockId, 'hardwood_chips');
      expect(result.feedstockAmount, 100.0);
      expect(result.biocharYield, 25.0);
      expect(result.carbonSequestered, 21.25);
      expect(result.pyrolysisTemperature, 450.0);
      expect(result.calculatedAt, testDate);
      expect(result.economicValue['totalValue'], 17.81);
    });

    test('should convert CalculatorResult to JSON', () {
      final json = testResult.toJson();

      expect(json['id'], 'result_123');
      expect(json['feedstock_id'], 'hardwood_chips');
      expect(json['feedstock_amount'], 100.0);
      expect(json['biochar_yield'], 25.0);
      expect(json['carbon_sequestered'], 21.25);
      expect(json['calculated_at'], testDate.toIso8601String());
      expect(json['economic_value']['totalValue'], 17.81);
    });

    test('should calculate CO2 equivalent correctly', () {
      expect(testResult.co2Equivalent, closeTo(77.99, 0.01)); // 21.25 * 3.67
      expect(testResult.co2EquivalentReduction, closeTo(77.99, 0.01));
    });

    test('should calculate yield percentage correctly', () {
      expect(testResult.yieldPercentage, 25.0); // (25/100) * 100
    });

    test('should calculate mass efficiency correctly', () {
      expect(testResult.massEfficiency, 25.0); // (25/100) * 100
    });

    test('should calculate dry feedstock amount correctly', () {
      expect(testResult.dryFeedstockAmount, 90.0); // 100 * (1 - 0.1)
    });

    test('should format date correctly', () {
      expect(testResult.formattedDate, '15/1/2024');
    });

    test('should handle missing optional fields in JSON', () {
      final json = {
        'id': 'result_123',
        'feedstock_id': 'hardwood_chips',
        'feedstock_amount': 100.0,
        'biochar_yield': 25.0,
        'carbon_content': 45.0,
        'carbon_sequestered': 21.25,
        'energy_content': 712.5,
        'economic_value': <String, double>{},
        'calculated_at': testDate.toIso8601String(),
        'parameters': <String, dynamic>{},
      };

      final result = CalculatorResult.fromJson(json);

      expect(result.carbonEfficiency, 0.0);
      expect(result.feedstockType, '');
      expect(result.pyrolysisTemperature, 0.0);
      expect(result.residenceTime, 0.0);
      expect(result.moistureContent, 0.0);
    });
  });
}