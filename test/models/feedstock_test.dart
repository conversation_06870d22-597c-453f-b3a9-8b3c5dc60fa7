import 'package:flutter_test/flutter_test.dart';
import 'package:biochar_app/models/feedstock.dart';

void main() {
  group('Feedstock', () {
    test('should create Feedstock from valid JSON', () {
      final json = {
        'id': 'test_id',
        'name': 'Test Feedstock',
        'description': 'Test description',
        'type': 'Woody',
        'category': 'Agricultural',
        'thumbnail': 'test.png',
        'yield_range': {'min': 20.0, 'max': 30.0},
        'pyrolysis_temp': '450',
        'considerations': 'Test considerations',
        'advantages': ['Advantage 1', 'Advantage 2'],
        'disadvantages': ['Disadvantage 1'],
        'sustainabilityScore': 8.5,
        'cost': 50.0,
        'costUnit': 'per ton',
        'processingRequirements': ['Drying', 'Grinding'],
        'bulkDensity': 600.0,
        'energyContent': 18.5,
        'biocharYield': 25.0,
        'pyrolysisTemp': 450.0,
        'moistureContent': 10.0,
        'ashContent': 5.0,
        'volatileContent': 75.0,
        'fixedCarbon': 20.0,
        'availability': 'Year-round',
        'seasonality': 'None',
        'carbonContent': 45.0,
      };

      final feedstock = Feedstock.fromJson(json);

      expect(feedstock.id, 'test_id');
      expect(feedstock.name, 'Test Feedstock');
      expect(feedstock.description, 'Test description');
      expect(feedstock.type, 'Woody');
      expect(feedstock.category, 'Agricultural');
      expect(feedstock.yieldRange.min, 20.0);
      expect(feedstock.yieldRange.max, 30.0);
      expect(feedstock.sustainabilityScore, 8.5);
      expect(feedstock.advantages.length, 2);
      expect(feedstock.disadvantages.length, 1);
    });

    test('should handle missing optional fields in JSON', () {
      final json = {
        'id': 'test_id',
        'name': 'Test Feedstock',
        'description': 'Test description',
        'type': 'Woody',
        'category': 'Agricultural',
      };

      final feedstock = Feedstock.fromJson(json);

      expect(feedstock.id, 'test_id');
      expect(feedstock.thumbnail, '');
      expect(feedstock.advantages, isEmpty);
      expect(feedstock.disadvantages, isEmpty);
      expect(feedstock.sustainabilityScore, 0.0);
      expect(feedstock.cost, 0.0);
      expect(feedstock.costUnit, 'per ton');
    });

    test('should convert Feedstock to JSON correctly', () {
      final feedstock = Feedstock(
        id: 'test_id',
        name: 'Test Feedstock',
        description: 'Test description',
        type: 'Woody',
        category: 'Agricultural',
        thumbnail: 'test.png',
        yieldRange: YieldRange(min: 20.0, max: 30.0),
        pyrolysisTemp: '450',
        considerations: 'Test considerations',
        advantages: ['Advantage 1'],
        disadvantages: ['Disadvantage 1'],
        sustainabilityScore: 8.5,
        cost: 50.0,
        costUnit: 'per ton',
        processingRequirements: ['Drying'],
        bulkDensity: 600.0,
        energyContent: 18.5,
        biocharYield: 25.0,
        pyrolysisTemperature: 450.0,
        moistureContent: 10.0,
        ashContent: 5.0,
        volatileMatter: 75.0,
        fixedCarbon: 20.0,
        availability: 'Year-round',
        seasonality: 'None',
        carbonContent: 45.0,
      );

      final json = feedstock.toJson();

      expect(json['id'], 'test_id');
      expect(json['name'], 'Test Feedstock');
      expect(json['yield_range']['min'], 20.0);
      expect(json['yield_range']['max'], 30.0);
      expect(json['sustainabilityScore'], 8.5);
      expect(json['advantages'], ['Advantage 1']);
    });
  });

  group('YieldRange', () {
    test('should create YieldRange from JSON', () {
      final json = {'min': 15.0, 'max': 25.0};
      final yieldRange = YieldRange.fromJson(json);

      expect(yieldRange.min, 15.0);
      expect(yieldRange.max, 25.0);
    });

    test('should convert YieldRange to JSON', () {
      final yieldRange = YieldRange(min: 15.0, max: 25.0);
      final json = yieldRange.toJson();

      expect(json['min'], 15.0);
      expect(json['max'], 25.0);
    });
  });
}