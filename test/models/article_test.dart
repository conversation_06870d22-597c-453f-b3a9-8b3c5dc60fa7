import 'package:flutter_test/flutter_test.dart';
import 'package:biochar_app/models/article.dart';

void main() {
  group('Article', () {
    test('should create Article from JSON', () {
      final json = {
        'id': 'article_1',
        'category': 'Basics',
        'title': 'Introduction to Biochar',
        'summary': 'Learn the basics of biochar production',
        'readTimeMinutes': 5,
        'content': [
          {
            'section': 'Introduction',
            'text': 'Biochar is a charcoal-like substance...'
          },
          {
            'section': 'Benefits',
            'text': 'Biochar has many environmental benefits...'
          }
        ],
        'last_updated': '2024-01-15'
      };

      final article = Article.fromJson(json);

      expect(article.id, 'article_1');
      expect(article.category, 'Basics');
      expect(article.title, 'Introduction to Biochar');
      expect(article.summary, 'Learn the basics of biochar production');
      expect(article.readTimeMinutes, 5);
      expect(article.content.length, 2);
      expect(article.content[0].section, 'Introduction');
      expect(article.content[0].text, 'Biochar is a charcoal-like substance...');
      expect(article.lastUpdated, '2024-01-15');
    });

    test('should convert Article to JSON', () {
      final article = Article(
        id: 'article_1',
        category: 'Basics',
        title: 'Introduction to Biochar',
        summary: 'Learn the basics of biochar production',
        readTimeMinutes: 5,
        content: [
          ContentSection(
            section: 'Introduction',
            text: 'Biochar is a charcoal-like substance...'
          ),
          ContentSection(
            section: 'Benefits',
            text: 'Biochar has many environmental benefits...'
          )
        ],
        lastUpdated: '2024-01-15',
      );

      final json = article.toJson();

      expect(json['id'], 'article_1');
      expect(json['category'], 'Basics');
      expect(json['title'], 'Introduction to Biochar');
      expect(json['readTimeMinutes'], 5);
      expect(json['content'], isA<List>());
      expect(json['content'].length, 2);
      expect(json['last_updated'], '2024-01-15');
    });
  });

  group('ContentSection', () {
    test('should create ContentSection from JSON', () {
      final json = {
        'section': 'Introduction',
        'text': 'This is the introduction text...'
      };

      final contentSection = ContentSection.fromJson(json);

      expect(contentSection.section, 'Introduction');
      expect(contentSection.text, 'This is the introduction text...');
    });

    test('should convert ContentSection to JSON', () {
      final contentSection = ContentSection(
        section: 'Introduction',
        text: 'This is the introduction text...',
      );

      final json = contentSection.toJson();

      expect(json['section'], 'Introduction');
      expect(json['text'], 'This is the introduction text...');
    });

    test('should provide compatibility getters', () {
      final contentSection = ContentSection(
        section: 'Introduction',
        text: 'This is the introduction text...',
      );

      expect(contentSection.title, 'Introduction');
      expect(contentSection.content, 'This is the introduction text...');
    });
  });
}