import 'package:flutter_test/flutter_test.dart';
import 'package:biochar_app/services/calculator_service.dart';
import 'package:biochar_app/models/feedstock.dart';

void main() {
  group('CalculatorService', () {
    late Feedstock testFeedstock;

    setUp(() {
      testFeedstock = Feedstock(
        id: 'test_hardwood',
        name: 'Test Hardwood',
        description: 'Test description',
        type: 'hardwood',
        category: 'Woody',
        thumbnail: '',
        yieldRange: YieldRange(min: 20.0, max: 30.0),
        pyrolysisTemp: '450',
        considerations: '',
        advantages: [],
        disadvantages: [],
        sustainabilityScore: 8.0,
        cost: 0.0,
        costUnit: 'per ton',
        processingRequirements: [],
        bulkDensity: 600.0,
        energyContent: 18.0,
        biocharYield: 25.0,
        pyrolysisTemperature: 450.0,
        moistureContent: 10.0,
        ashContent: 5.0,
        volatileMatter: 75.0,
        fixedCarbon: 20.0,
        availability: '',
        seasonality: '',
        carbonContent: 45.0,
      );
    });

    group('calculateBiocharYield', () {
      test('should calculate correct biochar yield for hardwood', () {
        final result = CalculatorService.calculateBiocharYield(
          feedstock: testFeedstock,
          feedstockAmount: 100.0,
          moistureContent: 10.0,
          pyrolysisTemperature: 450.0,
          residenceTime: 45.0,
        );

        expect(result.feedstockId, 'test_hardwood');
        expect(result.feedstockAmount, 100.0);
        expect(result.moistureContent, 10.0);
        expect(result.pyrolysisTemperature, 450.0);
        expect(result.residenceTime, 45.0);
        
        // Dry feedstock: 100kg * (1 - 0.1) = 90kg
        // Hardwood yield: 90kg * 0.25 = 22.5kg
        expect(result.biocharYield, closeTo(22.5, 0.1));
        
        // Carbon sequestered: 22.5kg * 0.85 = 19.125kg
        expect(result.carbonSequestered, closeTo(19.125, 0.1));
        
        // CO2 equivalent: 19.125kg * 3.67 = 70.2kg
        expect(result.co2Equivalent, closeTo(70.2, 0.1));
      });

      test('should apply temperature adjustments correctly', () {
        // Test low temperature (300°C)
        final lowTempResult = CalculatorService.calculateBiocharYield(
          feedstock: testFeedstock,
          feedstockAmount: 100.0,
          moistureContent: 0.0,
          pyrolysisTemperature: 300.0,
          residenceTime: 45.0,
        );

        // Test optimal temperature (450°C)
        final optimalTempResult = CalculatorService.calculateBiocharYield(
          feedstock: testFeedstock,
          feedstockAmount: 100.0,
          moistureContent: 0.0,
          pyrolysisTemperature: 450.0,
          residenceTime: 45.0,
        );

        // Test high temperature (700°C)
        final highTempResult = CalculatorService.calculateBiocharYield(
          feedstock: testFeedstock,
          feedstockAmount: 100.0,
          moistureContent: 0.0,
          pyrolysisTemperature: 700.0,
          residenceTime: 45.0,
        );

        expect(lowTempResult.biocharYield, lessThan(optimalTempResult.biocharYield));
        expect(highTempResult.biocharYield, lessThan(optimalTempResult.biocharYield));
      });

      test('should apply residence time adjustments correctly', () {
        // Test short time (15 minutes)
        final shortTimeResult = CalculatorService.calculateBiocharYield(
          feedstock: testFeedstock,
          feedstockAmount: 100.0,
          moistureContent: 0.0,
          pyrolysisTemperature: 450.0,
          residenceTime: 15.0,
        );

        // Test optimal time (45 minutes)
        final optimalTimeResult = CalculatorService.calculateBiocharYield(
          feedstock: testFeedstock,
          feedstockAmount: 100.0,
          moistureContent: 0.0,
          pyrolysisTemperature: 450.0,
          residenceTime: 45.0,
        );

        // Test long time (120 minutes)
        final longTimeResult = CalculatorService.calculateBiocharYield(
          feedstock: testFeedstock,
          feedstockAmount: 100.0,
          moistureContent: 0.0,
          pyrolysisTemperature: 450.0,
          residenceTime: 120.0,
        );

        expect(shortTimeResult.biocharYield, lessThan(optimalTimeResult.biocharYield));
        expect(longTimeResult.biocharYield, lessThan(optimalTimeResult.biocharYield));
      });

      test('should handle different feedstock types', () {
        final coconutShellsFeedstock = Feedstock(
          id: 'coconut_shells',
          name: 'Coconut Shells',
          description: 'Test',
          type: 'coconut shells',
          category: 'Agricultural',
          thumbnail: '',
          yieldRange: YieldRange(min: 35.0, max: 45.0),
          pyrolysisTemp: '450',
          considerations: '',
          advantages: [],
          disadvantages: [],
          sustainabilityScore: 9.0,
          cost: 0.0,
          costUnit: 'per ton',
          processingRequirements: [],
          bulkDensity: 500.0,
          energyContent: 20.0,
          biocharYield: 40.0,
          pyrolysisTemperature: 450.0,
          moistureContent: 5.0,
          ashContent: 2.0,
          volatileMatter: 70.0,
          fixedCarbon: 28.0,
          availability: '',
          seasonality: '',
          carbonContent: 50.0,
        );

        final result = CalculatorService.calculateBiocharYield(
          feedstock: coconutShellsFeedstock,
          feedstockAmount: 100.0,
          moistureContent: 0.0,
          pyrolysisTemperature: 450.0,
          residenceTime: 45.0,
        );

        // Coconut shells have higher yield (40%) than hardwood (25%)
        expect(result.biocharYield, closeTo(40.0, 0.1));
      });
    });

    group('calculateEconomicValue', () {
      test('should calculate economic value correctly', () {
        final result = CalculatorService.calculateBiocharYield(
          feedstock: testFeedstock,
          feedstockAmount: 100.0,
          moistureContent: 10.0,
          pyrolysisTemperature: 450.0,
          residenceTime: 45.0,
        );

        final economicValue = CalculatorService.calculateEconomicValue(result);

        expect(economicValue.containsKey('biocharValue'), true);
        expect(economicValue.containsKey('carbonCreditValue'), true);
        expect(economicValue.containsKey('totalValue'), true);
        expect(economicValue['totalValue'], 
               economicValue['biocharValue']! + economicValue['carbonCreditValue']!);
      });
    });

    group('validateInputs', () {
      test('should return no errors for valid inputs', () {
        final errors = CalculatorService.validateInputs(
          feedstockAmount: 100.0,
          moistureContent: 15.0,
          pyrolysisTemperature: 450.0,
          residenceTime: 45.0,
        );

        expect(errors.isEmpty, true);
      });

      test('should return error for zero feedstock amount', () {
        final errors = CalculatorService.validateInputs(
          feedstockAmount: 0.0,
          moistureContent: 15.0,
          pyrolysisTemperature: 450.0,
          residenceTime: 45.0,
        );

        expect(errors.containsKey('feedstockAmount'), true);
      });

      test('should return error for invalid moisture content', () {
        final errors = CalculatorService.validateInputs(
          feedstockAmount: 100.0,
          moistureContent: 110.0,
          pyrolysisTemperature: 450.0,
          residenceTime: 45.0,
        );

        expect(errors.containsKey('moistureContent'), true);
      });

      test('should return error for invalid temperature', () {
        final errors = CalculatorService.validateInputs(
          feedstockAmount: 100.0,
          moistureContent: 15.0,
          pyrolysisTemperature: 1000.0,
          residenceTime: 45.0,
        );

        expect(errors.containsKey('pyrolysisTemperature'), true);
      });

      test('should return error for invalid residence time', () {
        final errors = CalculatorService.validateInputs(
          feedstockAmount: 100.0,
          moistureContent: 15.0,
          pyrolysisTemperature: 450.0,
          residenceTime: 500.0,
        );

        expect(errors.containsKey('residenceTime'), true);
      });
    });
  });
}