# Biochar App Test Suite

This directory contains comprehensive tests for the Biochar Flutter application.

## Test Structure

### Unit Tests (`/models`, `/services`)
- **Models**: Test data serialization, validation, and business logic
  - `feedstock_test.dart` - Feedstock model and YieldRange tests
  - `calculator_test.dart` - Calculator input/output/history models
  - `calculator_result_test.dart` - Result model with computed properties
  - `article_test.dart` - Article and ContentSection models

- **Services**: Test business logic and calculations
  - `calculator_service_test.dart` - Biochar yield calculations, validations, economic values

### Widget Tests (`/widgets`)
- **UI Components**: Test widget rendering and interaction
  - `feedstock_card_test.dart` - FeedstockCard component behavior

### Integration Tests (`/integration`)
- **App Flows**: End-to-end user workflow testing
  - `app_integration_test.dart` - Navigation, calculator workflow, offline functionality

### Basic Tests
- `widget_test.dart` - Smoke test for app initialization

## Running Tests

### All Tests
```bash
flutter test --coverage
```

### Specific Test Categories
```bash
# Unit tests only
flutter test test/models test/services

# Widget tests only  
flutter test test/widgets

# Integration tests only
flutter test test/integration
```

### With Coverage
```bash
# Run coverage script (Windows)
test_coverage.bat

# Run coverage script (Unix/Mac)
./test_coverage.sh
```

## Test Coverage

The test suite aims for 80%+ code coverage across:
- Data models (JSON serialization/deserialization)
- Business logic (calculations, validations)
- UI components (rendering, interactions)
- User workflows (navigation, data flow)

## Test Principles

1. **Offline-First**: Tests verify app functionality without network dependency
2. **Data Integrity**: Validate JSON parsing and data model consistency
3. **Calculation Accuracy**: Verify biochar yield and carbon calculations
4. **User Experience**: Test navigation flows and UI responsiveness
5. **Error Handling**: Validate input validation and error states

## Test Data

Tests use realistic data representative of:
- Various feedstock types (hardwood, agricultural residues, etc.)
- Different pyrolysis conditions (temperature, time, moisture)
- Typical yield ranges and carbon content values
- Real-world economic scenarios

## Continuous Integration

Tests are designed to run in CI/CD environments:
- No external dependencies required
- Deterministic results
- Fast execution for rapid feedback
- Coverage reporting integration