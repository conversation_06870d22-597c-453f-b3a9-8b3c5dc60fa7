// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:biochar_app/main.dart';

void main() {
  testWidgets('Biochar app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const BiocharApp());

    // Wait for the app to settle
    await tester.pumpAndSettle();

    // Verify that the app loads with home screen
    expect(find.text('Welcome to Biochar Calculator'), findsOneWidget);
    expect(find.text('Quick Access'), findsOneWidget);
    
    // Verify that navigation tabs are present
    expect(find.text('Home'), findsOneWidget);
    expect(find.text('Knowledge'), findsOneWidget);
    // <PERSON><PERSON><PERSON> appears twice (tab and potentially in content), so check it exists
    expect(find.text('Calculator'), findsWidgets);
  });
}
