import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:biochar_app/main.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Biochar App Integration Tests', () {
    testWidgets('complete app navigation flow', (WidgetTester tester) async {
      await tester.pumpWidget(const BiocharApp());
      await tester.pumpAndSettle();

      // Verify app starts on home screen
      expect(find.text('Welcome to Biochar Calculator'), findsOneWidget);
      expect(find.text('Quick Access'), findsOneWidget);

      // Test navigation to Knowledge Hub
      await tester.tap(find.text('Knowledge'));
      await tester.pumpAndSettle();
      
      // Verify Knowledge Hub loads
      expect(find.text('Knowledge Hub'), findsOneWidget);

      // Test navigation to Calculator (tap the tab, not content)
      await tester.tap(find.text('Calculator').last);
      await tester.pumpAndSettle();
      
      // Verify Calculator loads
      expect(find.text('Biochar Calculator'), findsOneWidget);

      // Test navigation to Feedstock screen
      await tester.tap(find.text('Feedstock'));
      await tester.pumpAndSettle();
      
      // Verify Feedstock screen loads
      expect(find.text('Feedstock Database'), findsOneWidget);

      // Test navigation back to Home
      await tester.tap(find.text('Home'));
      await tester.pumpAndSettle();
      
      // Verify we're back on home screen
      expect(find.text('Welcome to Biochar Calculator'), findsOneWidget);
    });

    testWidgets('calculator workflow', (WidgetTester tester) async {
      await tester.pumpWidget(const BiocharApp());
      await tester.pumpAndSettle();

      // Navigate to Calculator (tap the tab)
      await tester.tap(find.text('Calculator').last);
      await tester.pumpAndSettle();

      // Verify calculator elements are present
      expect(find.text('Biochar Calculator'), findsOneWidget);
      expect(find.text('Select Feedstock'), findsOneWidget);
      expect(find.text('Feedstock Amount (kg)'), findsOneWidget);

      // Test feedstock selection (assuming dropdown exists)
      if (find.byType(DropdownButton).evaluate().isNotEmpty) {
        await tester.tap(find.byType(DropdownButton).first);
        await tester.pumpAndSettle();
        
        // Select first feedstock option
        await tester.tap(find.byType(DropdownMenuItem).first);
        await tester.pumpAndSettle();
      }

      // Enter feedstock amount
      final amountField = find.byType(TextFormField).first;
      if (amountField.evaluate().isNotEmpty) {
        await tester.enterText(amountField, '100');
        await tester.pumpAndSettle();
      }

      // Test calculate button (if it exists)
      final calculateButton = find.textContaining('Calculate');
      if (calculateButton.evaluate().isNotEmpty) {
        await tester.tap(calculateButton);
        await tester.pumpAndSettle();
      }
    });

    testWidgets('feedstock browsing workflow', (WidgetTester tester) async {
      await tester.pumpWidget(const BiocharApp());
      await tester.pumpAndSettle();

      // Navigate to Feedstock screen
      await tester.tap(find.text('Feedstock'));
      await tester.pumpAndSettle();

      // Verify feedstock list loads
      expect(find.text('Feedstock Database'), findsOneWidget);

      // Test search functionality (if search field exists)
      final searchField = find.byType(TextField);
      if (searchField.evaluate().isNotEmpty) {
        await tester.enterText(searchField, 'wood');
        await tester.pumpAndSettle();
      }

      // Test feedstock card interaction (tap first feedstock card if exists)
      final feedstockCards = find.byType(Card);
      if (feedstockCards.evaluate().isNotEmpty) {
        await tester.tap(feedstockCards.first);
        await tester.pumpAndSettle();
        
        // Should navigate to feedstock detail (test back navigation)
        final backButton = find.byType(BackButton);
        if (backButton.evaluate().isNotEmpty) {
          await tester.tap(backButton);
          await tester.pumpAndSettle();
        }
      }
    });

    testWidgets('knowledge hub browsing workflow', (WidgetTester tester) async {
      await tester.pumpWidget(const BiocharApp());
      await tester.pumpAndSettle();

      // Navigate to Knowledge Hub
      await tester.tap(find.text('Knowledge'));
      await tester.pumpAndSettle();

      // Verify knowledge hub loads
      expect(find.text('Knowledge Hub'), findsOneWidget);

      // Test article browsing (if articles exist)
      final articleCards = find.byType(Card);
      if (articleCards.evaluate().isNotEmpty) {
        // Tap first article
        await tester.tap(articleCards.first);
        await tester.pumpAndSettle();
        
        // Test back navigation from article
        final backButton = find.byType(BackButton);
        if (backButton.evaluate().isNotEmpty) {
          await tester.tap(backButton);
          await tester.pumpAndSettle();
        }
      }
    });

    testWidgets('app persistence test', (WidgetTester tester) async {
      await tester.pumpWidget(const BiocharApp());
      await tester.pumpAndSettle();

      // Navigate through different screens to test state persistence
      await tester.tap(find.text('Calculator').last);
      await tester.pumpAndSettle();
      
      await tester.tap(find.text('Feedstock'));
      await tester.pumpAndSettle();
      
      await tester.tap(find.text('Knowledge'));
      await tester.pumpAndSettle();
      
      await tester.tap(find.text('Home'));
      await tester.pumpAndSettle();

      // Verify we end up back at home and app is still functional
      expect(find.text('Welcome to Biochar Calculator'), findsOneWidget);
    });

    testWidgets('offline functionality test', (WidgetTester tester) async {
      // This test verifies the app works without network connectivity
      await tester.pumpWidget(const BiocharApp());
      await tester.pumpAndSettle();

      // Test that all static content loads (feedstocks, articles, etc.)
      await tester.tap(find.text('Feedstock'));
      await tester.pumpAndSettle();
      
      // Should still be able to browse feedstocks offline
      expect(find.text('Feedstock Database'), findsOneWidget);

      await tester.tap(find.text('Knowledge'));
      await tester.pumpAndSettle();
      
      // Should still be able to browse articles offline
      expect(find.text('Knowledge Hub'), findsOneWidget);

      await tester.tap(find.text('Calculator').last);
      await tester.pumpAndSettle();
      
      // Calculator should work offline
      expect(find.text('Biochar Calculator'), findsOneWidget);
    });
  });
}