# Overview
The Biochar App is a mobile application designed to educate users about biochar, provide tools for estimating biochar yield, and offer reference data on biomass feedstocks. It targets farmers, researchers, and sustainability enthusiasts, particularly those in low-connectivity or rural areas where internet access is unreliable. The app solves the problem of limited access to practical biochar knowledge and tools by offering a fully offline experience with no login required. Its value lies in empowering users to understand and produce biochar, improve soil health, and contribute to carbon sequestration, all through an accessible, cross-platform mobile app built with Flutter.

# Core Features
1. **Biochar Knowledge Hub**
   - **What it does**: Provides preloaded articles on biochar fundamentals, including what biochar is, how to make it, its applications, and environmental benefits.
   - **Why it’s important**: Educates users, especially novices, to understand biochar’s benefits and practical uses, fostering adoption in agriculture and sustainability practices.
   - **How it works**: Articles are stored locally in JSON format and displayed in a reader interface with category filters (e.g., What is Biochar, How to Make Biochar). Includes an article on simple, low-cost techniques for making biochar at home (e.g., Pit Method, Tin Can Method). Users can scroll through articles and filter by category.

2. **Biochar Calculator**
   - **What it does**: Allows users to estimate biochar yield and carbon storage based on feedstock type, weight, and pyrolysis temperature.
   - **Why it’s important**: Enables users to make informed decisions about biochar production, supporting practical application and environmental impact assessment.
   - **How it works**: Users input feedstock type (via dropdown), weight (in kg), and pyrolysis temperature range (Low/Medium/High). The app calculates outputs (yield in kg, carbon storage in kg CO₂-eq) using fixed coefficients stored locally, with results displayed in a simple interface.

3. **Feedstock Database**
   - **What it does**: Provides a searchable database of common biomass materials used for biochar production.
   - **Why it’s important**: Helps users select appropriate feedstocks based on availability and properties, enhancing production efficiency.
   - **How it works**: Stored locally in JSON, each feedstock entry includes name, thumbnail image, typical yield range, ideal pyrolysis temperature, and special considerations (e.g., drying, ash content). Displayed as scrollable cards with search and tap-to-view details.

# User Experience
- **User Personas**:
  - **Farmer (Primary)**: Small-scale farmers in rural areas with limited internet access, seeking to improve soil fertility using biochar. May have basic smartphone literacy.
  - **Researcher**: Agronomists or students studying biochar’s applications, needing quick access to reference data and calculations.
  - **Sustainability Enthusiast**: Individuals interested in eco-friendly practices, looking to learn about and experiment with biochar production.
- **Key User Flows**:
  - **Learning about Biochar**: User opens the app, navigates to the Knowledge Hub via the bottom navigation bar, selects a category (e.g., How to Make Biochar), and reads articles.
  - **Calculating Yield**: User selects the Calculator tab, chooses a feedstock from the dropdown, enters weight and temperature, and views estimated yield and carbon storage.
  - **Exploring Feedstocks**: User navigates to the Feedstock tab, searches or scrolls through cards, and taps to view detailed feedstock information.
- **UI/UX Considerations**:
  - Clean, minimal interface with earth-tone colors to reflect sustainability themes.
  - Bottom navigation bar with tabs: Home, Calculator, Feedstock, Knowledge.
  - Large text and intuitive controls for accessibility, especially for users with limited tech experience.
  - Offline-first design ensures all features work without internet connectivity.

# Technical Architecture
- **System Components**:
  - **Frontend**: Flutter (Dart) for cross-platform mobile app (Android and iOS).
  - **Data Storage**: Static JSON files for Knowledge Hub articles and Feedstock Database; SQLite for user preferences (if implemented, e.g., for saving calculator scenarios).
  - **Calculation Engine**: Offline logic in Dart for Biochar Calculator, using fixed coefficients for yield and carbon storage calculations.
- **Data Models**:
  - **Article**: `{ id: string, category: string, title: string, content: [{ section: string, text: string }], last_updated: string }`
  - **Feedstock**: `{ id: string, name: string, thumbnail: string, yield_range: { min: number, max: number }, pyrolysis_temp: string, considerations: string }`
  - **Calculator Input**: `{ feedstock_id: string, weight_kg: number, temp_range: string }`
  - **Calculator Output**: `{ yield_kg: number, carbon_storage_kg_co2eq: number }`
- **APIs and Integrations**: None, as the app is fully offline with no backend or external API calls.
- **Infrastructure Requirements**:
  - Local storage on device for JSON files, images, and SQLite database (if used).
  - No server infrastructure required.

# Development Roadmap
- **MVP Requirements**:
  - **Biochar Knowledge Hub**: Implement JSON-based article storage and reader interface with category filtering. Include core articles for all categories, including “Making Biochar at Home” with Pit and Tin Can methods.
  - **Biochar Calculator**: Build input form (dropdown for feedstock, text field for weight, radio buttons for temperature) and output display with basic calculations using fixed coefficients.
  - **Feedstock Database**: Create JSON-based database with at least 10 feedstock entries, including thumbnails, and implement scrollable card interface with basic search.
  - **UI/UX**: Develop minimal interface with bottom navigation bar, earth-tone theme, and offline functionality.
  - **Carbon Credit Tracking**: Implement tools for estimating and tracking carbon credits.

# Logical Dependency Chain
1. **Foundation**:
   - Set up Flutter project with Dart for cross-platform compatibility.
   - Define JSON data models for articles and feedstocks, and implement local storage (JSON files, optional SQLite setup).
   - Build core UI framework: bottom navigation bar, earth-tone theme, and basic layouts for tabs (Home, Calculator, Feedstock, Knowledge).
2. **Usable/Visible Frontend**:
   - Implement Knowledge Hub reader interface with static JSON articles, including “Making Biochar at Home.” Add category filtering.
   - Develop Feedstock Database with scrollable cards, search, and tap-to-view details, populated with initial feedstock data.
   - Create Biochar Calculator with input form and output display, using hardcoded coefficients for calculations.
3. **Feature Completion**:
   - Refine Knowledge Hub with scrollable text and polished UI.
   - Enhance Feedstock Database with optimized image loading and search functionality.
   - Add error handling and basic input validation to Calculator.
4. **Polish and Optimization**:
   - Optimize storage (compress JSON, images) and performance for low-end devices.
   - Test accessibility (e.g., large text, screen reader support).
   - Add onboarding tutorial as a local asset for first-time users.

# Risks and Mitigations
- **Technical Challenges**:
  - **Risk**: JSON and image storage may increase app size, impacting low-end devices.
    - **Mitigation**: Compress images (e.g., PNGs) and optimize JSON structure. Limit initial feedstock entries and article count.
  - **Risk**: Calculator accuracy depends on coefficient quality, which may vary by region.
    - **Mitigation**: Include a disclaimer in the Calculator UI about regional variability and provide references to feedstock data in the Knowledge Hub.
- **MVP Scope**:
  - **Risk**: Overloading MVP with features could delay release.
    - **Mitigation**: Prioritize core functionality (basic Knowledge Hub, Calculator, Feedstock Database) and defer enhancements (e.g., charts, multilingual support) to future phases.
  - **Risk**: App may not meet needs of non-technical users (e.g., farmers).
    - **Mitigation**: Include onboarding tutorial and test with target users to ensure intuitive design.
- **Resource Constraints**:
  - **Risk**: Limited development resources for Flutter expertise or testing on diverse devices.
    - **Mitigation**: Use Flutter’s built-in tools for cross-platform testing and focus on open-source libraries to reduce development time. Leverage community feedback for testing.

# Appendix
- **Research Findings**:
  - Biochar adoption is low in rural areas due to lack of accessible education and tools, validating the need for an offline app.
  - Common feedstocks (e.g., wood chips, crop residues) vary by region, so the Feedstock Database should include globally relevant options with clear considerations.
- **Technical Specifications**:
  - Flutter version: Latest stable release at development time.
  - Minimum device requirements: Android 5.0+ or iOS 12.0+, 1GB RAM, 100MB storage.
  - Image format: Compressed PNG for feedstock thumbnails.
  - JSON file size: Target <500KB for articles, <200KB for feedstocks.