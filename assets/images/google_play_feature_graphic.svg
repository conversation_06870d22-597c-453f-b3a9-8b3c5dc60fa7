<svg width="1024" height="500" viewBox="0 0 1024 500" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B4513;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#A0522D;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#654321;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="leafGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#32CD32;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#228B22;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="fireGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#FF6347;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC143C;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- Main background -->
  <rect width="1024" height="500" fill="url(#bgGradient)"/>
  
  <!-- Decorative biochar particles -->
  <g opacity="0.3">
    <circle cx="100" cy="80" r="4" fill="#2F2F2F"/>
    <circle cx="200" cy="120" r="3" fill="#2F2F2F"/>
    <circle cx="300" cy="60" fill="#2F2F2F"/>
    <circle cx="850" cy="100" r="5" fill="#2F2F2F"/>
    <circle cx="920" cy="150" r="3" fill="#2F2F2F"/>
    <circle cx="780" cy="80" r="4" fill="#2F2F2F"/>
  </g>
  
  <!-- Left side: App icon and branding -->
  <g transform="translate(80,150)">
    <!-- Simplified app icon -->
    <circle cx="100" cy="100" r="80" fill="#654321" stroke="#2F4F2F" stroke-width="4"/>
    
    <!-- Fire element -->
    <path d="M 100 60 Q 85 45 80 30 Q 85 20 95 25 Q 105 15 115 25 Q 120 20 125 30 Q 115 45 100 60 Z" fill="url(#fireGradient)"/>
    
    <!-- Leaves -->
    <path d="M 70 80 Q 55 70 50 55 Q 55 50 65 52 Q 70 45 75 52 Q 80 50 85 55 Q 80 70 70 80 Z" fill="url(#leafGradient)"/>
    <path d="M 130 85 Q 145 75 150 60 Q 145 55 135 57 Q 130 50 125 57 Q 120 55 115 60 Q 120 75 130 85 Z" fill="url(#leafGradient)"/>
    
    <!-- Carbon representation -->
    <g transform="translate(100,130)" opacity="0.8">
      <circle cx="0" cy="0" r="8" fill="#2F2F2F"/>
      <circle cx="-12" cy="-6" r="5" fill="#2F2F2F"/>
      <circle cx="12" cy="-6" r="5" fill="#2F2F2F"/>
      <line x1="0" y1="0" x2="-12" y2="-6" stroke="#2F2F2F" stroke-width="2"/>
      <line x1="0" y1="0" x2="12" y2="-6" stroke="#2F2F2F" stroke-width="2"/>
    </g>
  </g>
  
  <!-- Center: Main title and tagline -->
  <g transform="translate(300,150)">
    <text x="0" y="40" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#F5F5DC">BIOCHAR</text>
    <text x="0" y="80" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#90EE90">PRODUCTION</text>
    <text x="0" y="110" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#90EE90">ASSISTANT</text>
    
    <text x="0" y="150" font-family="Arial, sans-serif" font-size="18" fill="#F5F5DC" opacity="0.9">Turn Agricultural Waste into Valuable Soil Carbon</text>
    <text x="0" y="175" font-family="Arial, sans-serif" font-size="16" fill="#F5F5DC" opacity="0.8">✓ Offline Calculator  ✓ Knowledge Hub  ✓ Carbon Tracking</text>
  </g>
  
  <!-- Right side: Feature highlights -->
  <g transform="translate(650,100)">
    <!-- Calculator icon -->
    <rect x="0" y="0" width="60" height="80" rx="8" fill="#2F4F2F" stroke="#90EE90" stroke-width="2"/>
    <rect x="10" y="10" width="40" height="15" fill="#90EE90"/>
    <circle cx="20" cy="40" r="4" fill="#90EE90"/>
    <circle cx="30" cy="40" r="4" fill="#90EE90"/>
    <circle cx="40" cy="40" r="4" fill="#90EE90"/>
    <circle cx="20" cy="55" r="4" fill="#90EE90"/>
    <circle cx="30" cy="55" r="4" fill="#90EE90"/>
    <circle cx="40" cy="55" r="4" fill="#90EE90"/>
    <text x="75" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#F5F5DC">Smart Calculator</text>
    <text x="75" y="40" font-family="Arial, sans-serif" font-size="12" fill="#F5F5DC" opacity="0.8">Accurate yield estimates</text>
    
    <!-- Knowledge icon -->
    <g transform="translate(0,120)">
      <rect x="0" y="0" width="50" height="60" rx="5" fill="#2F4F2F" stroke="#90EE90" stroke-width="2"/>
      <rect x="10" y="10" width="30" height="3" fill="#90EE90"/>
      <rect x="10" y="20" width="25" height="2" fill="#90EE90"/>
      <rect x="10" y="28" width="30" height="2" fill="#90EE90"/>
      <rect x="10" y="36" width="20" height="2" fill="#90EE90"/>
      <text x="65" y="20" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#F5F5DC">Knowledge Hub</text>
      <text x="65" y="35" font-family="Arial, sans-serif" font-size="12" fill="#F5F5DC" opacity="0.8">Complete offline guides</text>
    </g>
    
    <!-- Carbon tracking icon -->
    <g transform="translate(0,240)">
      <circle cx="25" cy="25" r="20" fill="none" stroke="#90EE90" stroke-width="3"/>
      <path d="M 15 25 L 22 32 L 35 18" stroke="#90EE90" stroke-width="3" fill="none"/>
      <text x="60" y="20" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#F5F5DC">Carbon Tracking</text>
      <text x="60" y="35" font-family="Arial, sans-serif" font-size="12" fill="#F5F5DC" opacity="0.8">Monitor your impact</text>
    </g>
  </g>
  
  <!-- Bottom tagline -->
  <text x="512" y="450" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#F5F5DC">🌱 Sustainable Agriculture • Climate Action • Soil Health 🔥</text>
  
  <!-- Subtle overlay for depth -->
  <rect width="1024" height="500" fill="url(#bgGradient)" opacity="0.1"/>
</svg>