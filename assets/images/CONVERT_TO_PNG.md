# Converting SVG Icons to PNG for Google Play Store

## Quick Conversion Guide

Google Play Store requires PNG or JPG files, not SVG. Follow these steps to convert your SVG icons:

## Required Files for Google Play

1. **App Icon**: `app_icon_512.png` (512×512px)
2. **Feature Graphic**: `google_play_feature_graphic.png` (1024×500px)

## Conversion Methods

### Option 1: Online Converter (Easiest)

1. Go to any of these free online converters:
   - https://convertio.co/svg-png/
   - https://cloudconvert.com/svg-to-png
   - https://svg2png.com/

2. Upload your SVG files:
   - `app_icon.svg`
   - `google_play_feature_graphic.svg`

3. Set the output dimensions:
   - App icon: 512×512 pixels
   - Feature graphic: 1024×500 pixels

4. Download the converted PNG files

5. Rename them:
   - `app_icon_512.png`
   - `google_play_feature_graphic.png`

### Option 2: Using Software

**Inkscape (Free)**
```bash
# For app icon
inkscape --export-type=png --export-width=512 --export-height=512 --export-filename=app_icon_512.png app_icon.svg

# For feature graphic
inkscape --export-type=png --export-width=1024 --export-height=500 --export-filename=google_play_feature_graphic.png google_play_feature_graphic.svg
```

**GIMP (Free)**
1. Open SVG file in GIMP
2. Set import size to required dimensions
3. Export as PNG

## File Requirements

- **Format**: PNG (preferred) or JPG
- **Size limits**: Under 1MB each
- **Transparency**: Supported in PNG
- **Quality**: High resolution for best appearance

## After Conversion

Place the converted PNG files in this directory alongside the SVG originals:
- `app_icon_512.png`
- `google_play_feature_graphic.png`

These PNG files are ready for Google Play Store submission!