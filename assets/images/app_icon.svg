<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <radialGradient id="bgGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#8B4513;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#654321;stop-opacity:1" />
    </radialGradient>
    <linearGradient id="leafGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#228B22;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#006400;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="fireGradient" cx="50%" cy="80%" r="60%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FF6347;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC143C;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- Main background -->
  <circle cx="256" cy="256" r="240" fill="url(#bgGradient)" stroke="#2F4F2F" stroke-width="8"/>
  
  <!-- Biochar particles (small dark circles) -->
  <circle cx="180" cy="200" r="8" fill="#2F2F2F" opacity="0.8"/>
  <circle cx="320" cy="180" r="6" fill="#2F2F2F" opacity="0.7"/>
  <circle cx="200" cy="320" r="7" fill="#2F2F2F" opacity="0.6"/>
  <circle cx="350" cy="300" r="5" fill="#2F2F2F" opacity="0.8"/>
  <circle cx="150" cy="280" r="6" fill="#2F2F2F" opacity="0.7"/>
  
  <!-- Central fire/flame element -->
  <path d="M 256 180 Q 240 160 230 140 Q 235 120 250 130 Q 265 115 280 130 Q 285 120 290 140 Q 280 160 256 180 Z" fill="url(#fireGradient)" opacity="0.9"/>
  
  <!-- Leaf elements representing growth/sustainability -->
  <path d="M 200 150 Q 180 130 170 110 Q 175 100 190 105 Q 200 95 210 105 Q 215 100 220 110 Q 210 130 200 150 Z" fill="url(#leafGradient)" opacity="0.8"/>
  <path d="M 320 160 Q 340 140 350 120 Q 345 110 330 115 Q 320 105 310 115 Q 305 110 300 120 Q 310 140 320 160 Z" fill="url(#leafGradient)" opacity="0.8"/>
  
  <!-- Carbon molecule representation (simplified) -->
  <g transform="translate(256,350)" opacity="0.7">
    <circle cx="0" cy="0" r="12" fill="#2F2F2F"/>
    <circle cx="-20" cy="-10" r="8" fill="#2F2F2F"/>
    <circle cx="20" cy="-10" r="8" fill="#2F2F2F"/>
    <circle cx="0" cy="20" r="8" fill="#2F2F2F"/>
    <line x1="0" y1="0" x2="-20" y2="-10" stroke="#2F2F2F" stroke-width="3"/>
    <line x1="0" y1="0" x2="20" y2="-10" stroke="#2F2F2F" stroke-width="3"/>
    <line x1="0" y1="0" x2="0" y2="20" stroke="#2F2F2F" stroke-width="3"/>
  </g>
  
  <!-- App name text (optional, can be removed for cleaner icon) -->
  <text x="256" y="420" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#F5F5DC" opacity="0.9">BIOCHAR</text>
  
  <!-- Subtle border highlight -->
  <circle cx="256" cy="256" r="240" fill="none" stroke="#8FBC8F" stroke-width="2" opacity="0.5"/>
</svg>