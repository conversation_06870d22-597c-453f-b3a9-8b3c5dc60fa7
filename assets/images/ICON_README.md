# Biochar App Icon Assets

This directory contains all icon and graphic assets for the Biochar Production Assistant app.

## Icon Files

### 1. `app_icon.svg` - Main App Icon
- **Size**: 512x512px
- **Format**: SVG (vector)
- **Usage**: Primary app icon for all platforms
- **Features**: 
  - Earth-tone color scheme (browns and greens)
  - Central flame element representing biochar production
  - Leaf elements symbolizing sustainability
  - Carbon molecule representation
  - Biochar particles scattered throughout
  - App name text included

### 2. `app_icon_simple.svg` - Simplified Version
- **Size**: 512x512px
- **Format**: SVG (vector)
- **Usage**: Smaller icon sizes, launcher icons
- **Features**:
  - Cleaner, more readable at small sizes
  - Larger, more prominent elements
  - Simplified carbon symbol with "C" text
  - Fewer but more visible biochar particles

### 3. `app_icon_high_contrast.svg` - Accessibility Version
- **Size**: 512x512px
- **Format**: SVG (vector)
- **Usage**: High contrast themes, accessibility compliance
- **Features**:
  - White background with black elements
  - High contrast colors (black, white, orange, green)
  - Bold outlines for better visibility
  - Meets WCAG accessibility standards

### 4. `google_play_feature_graphic.svg` - Store Feature Graphic
- **Size**: 1024x500px
- **Format**: SVG (vector)
- **Usage**: Google Play Store feature graphic
- **Features**:
  - Horizontal layout optimized for store listing
  - App branding and title
  - Key feature highlights with icons
  - Professional marketing presentation
  - Tagline and feature bullets

## Design Elements

### Color Palette
- **Primary Brown**: #8B4513 (Saddle Brown)
- **Secondary Brown**: #654321 (Dark Brown)
- **Accent Brown**: #A0522D (Sienna)
- **Primary Green**: #228B22 (Forest Green)
- **Accent Green**: #90EE90 (Light Green)
- **Fire Colors**: #FFD700 (Gold) → #FF6347 (Tomato) → #DC143C (Crimson)
- **Text**: #F5F5DC (Beige)
- **Carbon/Biochar**: #2F2F2F (Dark Gray)

### Symbolism
- **Flame**: Represents the pyrolysis process of biochar production
- **Leaves**: Symbolize sustainability, agriculture, and environmental benefits
- **Carbon Symbol**: Represents carbon sequestration and storage
- **Biochar Particles**: Small dark circles representing the end product
- **Earth Tones**: Connect to soil, agriculture, and natural processes

## Usage Guidelines

### For Developers
1. Use `app_icon.svg` for the main app icon in pubspec.yaml
2. Generate PNG versions in required sizes (48, 72, 96, 144, 192px) for Android
3. Generate PNG versions for iOS (29, 40, 58, 60, 80, 87, 120, 180px)
4. Use `app_icon_simple.svg` for very small sizes (< 48px)
5. Use `app_icon_high_contrast.svg` for accessibility themes

### For Google Play Store
**IMPORTANT**: Google Play Store does NOT accept SVG files. All graphics must be converted to PNG or JPG format.

1. **App Icon**: Convert `app_icon.svg` to PNG (512x512px) - Required
2. **Feature Graphic**: Convert `google_play_feature_graphic.svg` to PNG (1024x500px) - Required
3. **Screenshots**: Create PNG screenshots of the app in action
4. **Promo Graphic**: Optional 180x120px PNG for promotional use

**Required PNG Conversions for Google Play**:
- High-res icon: 512x512px PNG (from app_icon.svg)
- Feature graphic: 1024x500px PNG (from google_play_feature_graphic.svg)
- All files must be under 1MB in size

### For Other Platforms
- **Web**: Use SVG format for scalability
- **Desktop**: Generate ICO files from main icon
- **Marketing**: Use feature graphic elements for promotional materials

## File Conversion

### Converting SVG to PNG for Google Play Store

**Method 1: Online Converters (Easiest)**
- Upload SVG files to online converters like:
  - convertio.co
  - cloudconvert.com
  - svg2png.com
- Set output size to required dimensions
- Download PNG files

**Method 2: Using Inkscape (Recommended for Quality)**
```bash
# Install Inkscape first, then run:
# For app icon (512x512)
inkscape --export-type=png --export-width=512 --export-height=512 --export-filename=app_icon_512.png app_icon.svg

# For feature graphic (1024x500)
inkscape --export-type=png --export-width=1024 --export-height=500 --export-filename=google_play_feature_graphic.png google_play_feature_graphic.svg
```

**Method 3: Using ImageMagick**
```bash
# For app icon
convert -background transparent app_icon.svg -resize 512x512 app_icon_512.png

# For feature graphic
convert -background transparent google_play_feature_graphic.svg -resize 1024x500 google_play_feature_graphic.png
```

**Method 4: Using GIMP**
1. Open SVG file in GIMP
2. Set import resolution to desired size
3. Export as PNG with transparency

### Required PNG Files for Google Play Submission
After conversion, you should have:
- `app_icon_512.png` (512x512px, <1MB)
- `google_play_feature_graphic.png` (1024x500px, <1MB)

## Platform-Specific Requirements

### Android
- Adaptive icons supported
- Material Design guidelines compliance
- Multiple density support (mdpi, hdpi, xhdpi, xxhdpi, xxxhdpi)

### iOS
- App Store guidelines compliance
- Multiple size requirements for different devices
- No transparency in app icons

### Google Play Store
- Feature graphic: 1024x500px
- App icon: 512x512px
- High-res icon: 512x512px
- Screenshots: Various sizes depending on device type

## Brand Consistency

All icons maintain consistent:
- Color palette reflecting biochar and sustainability themes
- Visual elements (flame, leaves, carbon symbols)
- Typography (Arial/sans-serif family)
- Overall aesthetic aligned with app's earth-tone theme

## Notes

- All SVG files are optimized for web and print
- Icons are designed to be recognizable at any size
- Color choices reflect the app's focus on environmental sustainability
- Design elements are culturally neutral and globally appropriate
- Files are ready for immediate use in app development and store submission