{"calculation_coefficients": {"base_yield_factors": {"wood_chips": 0.2, "rice_husks": 0.25, "corn_stover": 0.23, "coconut_shells": 0.3, "bamboo": 0.25, "wheat_straw": 0.2, "pine_needles": 0.16, "sugarcane_bagasse": 0.2}, "temperature_modifiers": {"low": {"range": "300-400°C", "multiplier": 0.85, "description": "Lower temperature produces more biochar but less stable"}, "medium": {"range": "400-500°C", "multiplier": 1.0, "description": "Optimal temperature for most feedstocks"}, "high": {"range": "500-600°C", "multiplier": 0.75, "description": "Higher temperature produces less but more stable biochar"}}, "moisture_adjustments": {"dry": {"range": "<10%", "multiplier": 1.0, "description": "Optimal moisture content"}, "moderate": {"range": "10-20%", "multiplier": 0.9, "description": "Acceptable moisture level"}, "wet": {"range": ">20%", "multiplier": 0.7, "description": "High moisture reduces yield significantly"}}, "particle_size_factors": {"fine": {"range": "<5mm", "multiplier": 1.1, "description": "Small particles heat more evenly"}, "medium": {"range": "5-20mm", "multiplier": 1.0, "description": "Standard particle size"}, "coarse": {"range": ">20mm", "multiplier": 0.85, "description": "Large particles may not heat uniformly"}}, "carbon_sequestration": {"carbon_content_ratio": 0.7, "co2_equivalent_factor": 3.67, "stability_factor": 0.9, "description": "Assumes 70% carbon content, 90% stability over 100 years"}, "formulas": {"biochar_yield": "base_yield * temperature_modifier * moisture_adjustment * particle_size_factor", "carbon_sequestered": "biochar_yield * carbon_content_ratio * co2_equivalent_factor * stability_factor", "soil_application_rate": "biochar_yield * 0.1", "description": "Standard formulas for biochar calculations"}, "units": {"input_biomass": "kg", "biochar_output": "kg", "carbon_sequestered": "kg CO2 equivalent", "soil_area": "square meters", "application_rate": "kg per square meter"}}}