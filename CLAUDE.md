# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
This is a Flutter mobile application for biochar education and production planning. The app provides offline functionality for farmers, researchers, and sustainability enthusiasts to learn about biochar, calculate yields, and explore feedstock options. It targets rural users with limited internet connectivity.

## Development Commands

### Essential Flutter Commands (run from repository root)
```bash
# Install dependencies
flutter pub get

# Run app in debug mode with hot reload
flutter run

# Static analysis and linting
flutter analyze
dart format --fix .

# Run tests
flutter test

# Build for Android
flutter build apk
flutter build appbundle

# Clean build artifacts
flutter clean
```

### Code Quality Pipeline
Before any code changes:
1. `flutter analyze` - Check for lint issues
2. `dart format --fix .` - Format code
3. `flutter test` - Run all tests
4. `flutter build apk --debug` - Verify build succeeds

### Troubleshooting
If you encounter Gradle cache issues like "Could not read workspace metadata":
```bash
# Clean Flutter artifacts
flutter clean

# Regenerate dependencies  
flutter pub get

# If Gradle cache is corrupted, manually delete (when no Gradle processes running):
# rmdir /s "C:\Users\<USER>\.gradle\caches"
```

## Architecture

### Project Structure
- **`lib/`** - Main application code
  - `models/` - Data classes (Article, Feedstock, Calculator, etc.)
  - `services/` - Business logic (DatabaseHelper, ArticleService, etc.)
  - `screens/` - UI screens for each feature
  - `widgets/` - Reusable UI components
  - `theme/` - App theming with earth-tone colors
- **`assets/`** - Static assets
  - `data/` - JSON files for articles, feedstocks, calculation coefficients
  - `images/` - Feedstock thumbnails
- **`test/`** - Unit and widget tests
- **`android/`, `ios/`, `web/`, `windows/`, `linux/`, `macos/`** - Platform-specific code

### Key Technologies
- **Flutter/Dart** - Cross-platform mobile framework
- **Provider** - State management pattern
- **sqflite** - Local SQLite database for user preferences and history
- **JSON assets** - Offline data storage for articles and feedstocks

### Data Flow
- Static content loaded from JSON assets in `assets/data/`
- User data and calculator history stored in SQLite via `DatabaseHelper`
- State managed through Provider pattern
- Fully offline-first architecture

## Development Guidelines

### Code Style
- Follows `flutter_lints` package recommendations
- PascalCase for classes, camelCase for methods/variables
- Private members prefixed with underscore
- Null safety enabled (Dart 3.8.1+)

### Performance Constraints
- JSON files: articles <500KB, feedstocks <200KB
- Target devices: Android 5.0+/iOS 12.0+, 1GB RAM minimum
- Optimize images (compressed PNG format)

### Adding Features
1. Create data models in `models/`
2. Implement business logic in `services/`
3. Build UI in `screens/` and `widgets/`
4. Add JSON data to `assets/data/` if needed
5. Update `MainScreen` navigation if adding new tabs
6. Write corresponding tests

### Common Patterns
- Singleton pattern for `DatabaseHelper`
- Future/async for all database and file operations
- Provider for state management across widgets
- Bottom navigation structure in `MainScreen`